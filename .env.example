# Audio Processing Configuration
# Copy this file to .env and update with your actual values

# Service Selection
# Choose which service to use: "elevenlabs" or "resemble"
DEFAULT_SERVICE=resemble

# ElevenLabs Configuration
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
ELEVENLABS_VOICE_ID=your_voice_id_here
ELEVENLABS_MODEL=eleven_english_sts_v2
ELEVENLABS_STABILITY=0.5
ELEVENLABS_SIMILARITY_BOOST=0.8
ELEVENLABS_STYLE=0.0
ELEVENLABS_SPEAKER_BOOST=true

# Resemble.ai Configuration
RESEMBLE_API_KEY=your_resemble_api_key_here
RESEMBLE_PROJECT_UUID=your_project_uuid_here
RESEMBLE_VOICE_UUID=your_voice_uuid_here

# Audio Processing Settings
INPUT_DIRECTORY=.
OUTPUT_DIRECTORY=processed
FINAL_OUTPUT_FILE=final_output.mp3
FRAGMENT_PREFIX=output_fragment
PROCESSED_PREFIX=processed_

# Audio Fragmentation Settings
MAX_FRAGMENT_DURATION=240          # Maximum duration per fragment in seconds (4 minutes)
SILENCE_THRESHOLD=-30              # Silence threshold in dB for splitting
MIN_SILENCE_DURATION=0.5           # Minimum silence duration to split on (seconds)

# Server Configuration (for Resemble.ai)
SERVER_PORT=3000
SERVER_HOST=localhost
