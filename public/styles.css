/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 20px;
}

.quick-start {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    backdrop-filter: blur(10px);
}

.quick-start h3 {
    margin-bottom: 15px;
    color: white;
}

.quick-start ol {
    text-align: left;
    max-width: 600px;
    margin: 0 auto;
    color: white;
}

.quick-start li {
    margin-bottom: 8px;
    line-height: 1.4;
}

/* Cards */
.card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.card h2 {
    margin-bottom: 20px;
    color: #4a5568;
    font-size: 1.3rem;
}

/* File Input */
.file-input-container {
    position: relative;
    margin-bottom: 15px;
}

#audioFile {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-input-label {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    border: 2px dashed #cbd5e0;
    border-radius: 8px;
    background: #f7fafc;
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-input-label:hover {
    border-color: #667eea;
    background: #edf2f7;
}

.file-input-label.has-file {
    border-color: #48bb78;
    background: #f0fff4;
}

.file-icon {
    font-size: 1.5rem;
    margin-right: 10px;
}

.file-text {
    font-weight: 500;
}

.file-info {
    padding: 15px;
    background: #e6fffa;
    border-radius: 6px;
    border-left: 4px solid #38b2ac;
}

.demo-section {
    margin-top: 15px;
    padding: 15px;
    background: #f7fafc;
    border-radius: 6px;
    border-left: 4px solid #667eea;
}

.demo-section p {
    margin: 5px 0;
    color: #4a5568;
    font-size: 0.9rem;
}

/* Options Grid */
.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.option-group {
    display: flex;
    flex-direction: column;
}

.option-group label {
    font-weight: 500;
    margin-bottom: 5px;
    color: #4a5568;
}

.option-group input[type="number"] {
    padding: 10px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 1rem;
}

.option-group input[type="checkbox"] {
    margin-right: 8px;
}

.option-group small {
    color: #718096;
    font-size: 0.85rem;
    margin-top: 5px;
}

/* Actions Grid */
.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

/* Buttons */
.btn {
    padding: 15px 20px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 80px;
    justify-content: center;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn small {
    font-size: 0.8rem;
    font-weight: 400;
    margin-top: 5px;
    opacity: 0.8;
}

.btn-info {
    background: #3182ce;
    color: white;
}

.btn-info:hover:not(:disabled) {
    background: #2c5282;
}

.btn-warning {
    background: #ed8936;
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background: #dd6b20;
}

.btn-danger {
    background: #e53e3e;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #c53030;
}

.btn-success {
    background: #38a169;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #2f855a;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-secondary {
    background: #a0aec0;
    color: white;
}

/* Progress */
.progress-container {
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.9rem;
    color: #4a5568;
}

/* Log Container */
.log-container {
    max-height: 300px;
    overflow-y: auto;
    background: #1a202c;
    border-radius: 6px;
    padding: 15px;
}

.log-output {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85rem;
    color: #e2e8f0;
    white-space: pre-wrap;
    line-height: 1.4;
}

/* Results */
.results-container {
    min-height: 100px;
}

.no-results {
    color: #718096;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f7fafc;
    border-radius: 6px;
    margin-bottom: 10px;
}

.result-item .download-btn {
    padding: 5px 15px;
    background: #667eea;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 0.9rem;
}

/* Status Bar */
.status-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #e2e8f0;
}

.connection-status {
    font-size: 0.9rem;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #718096;
}

.threshold-results {
    font-family: monospace;
    font-size: 0.9rem;
    background: #f7fafc;
    padding: 15px;
    border-radius: 6px;
    max-height: 400px;
    overflow-y: auto;
}

/* Utility classes */
.hidden {
    display: none !important;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .options-grid,
    .actions-grid {
        grid-template-columns: 1fr;
    }
    
    .status-bar {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
}
