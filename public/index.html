<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎵 Pilgrim Audio Breaker</title>
    <link rel="stylesheet" href="styles.css">
</head>

<body>
    <div class="container">
        <header>
            <h1>🎵 Pilgrim Audio Breaker</h1>
            <p>Intelligent audio fragmentation and voice processing</p>
            <div class="quick-start">
                <h3>🚀 Quick Start Guide</h3>
                <ol>
                    <li><strong>📁 Select</strong> your audio file using the file picker below</li>
                    <li><strong>🔍 Test</strong> with "Find Optimal Threshold" (free, no API costs)</li>
                    <li><strong>✂️ Fragment</strong> your audio to see how it breaks (free)</li>
                    <li><strong>🤖 Process</strong> with AI when you're ready (costs money)</li>
                </ol>
            </div>
        </header>

        <main>
            <!-- File Selection Section -->
            <section class="card">
                <h2>📁 Select Audio File</h2>
                <div class="file-input-container">
                    <input type="file" id="audioFile" accept=".mp3,.wav,.m4a,.aac,.flac,.ogg,.wma" />
                    <label for="audioFile" class="file-input-label">
                        <span class="file-icon">📎</span>
                        <span class="file-text">Choose audio file...</span>
                    </label>
                </div>
                <div id="fileInfo" class="file-info hidden"></div>

                <div class="demo-section">
                    <p><strong>💡 Tip:</strong> Supported formats: MP3, WAV, M4A, AAC, FLAC, OGG, WMA (max 500MB)</p>
                    <p><strong>🔒 Privacy:</strong> Files are processed locally and automatically deleted after
                        processing</p>
                </div>
            </section>

            <!-- Options Section -->
            <section class="card">
                <h2>⚙️ Processing Options</h2>
                <div class="options-grid">
                    <div class="option-group">
                        <label for="maxDuration">Target Duration (seconds):</label>
                        <input type="number" id="maxDuration" value="240" min="60" max="260" />
                        <small>Max 260s (hard limit)</small>
                    </div>

                    <div class="option-group">
                        <label for="silenceThreshold">Silence Threshold (dB):</label>
                        <input type="number" id="silenceThreshold" value="-40" min="-80" max="-10" />
                        <small>More negative = more sensitive</small>
                    </div>

                    <div class="option-group">
                        <label for="minSilenceDuration">Min Silence Duration (s):</label>
                        <input type="number" id="minSilenceDuration" value="0.3" min="0.1" max="2" step="0.1" />
                        <small>Minimum pause length to detect</small>
                    </div>

                    <div class="option-group">
                        <label>
                            <input type="checkbox" id="cleanup" />
                            Clean up fragment files after processing
                        </label>
                    </div>
                </div>
            </section>

            <!-- Action Buttons Section -->
            <section class="card">
                <h2>🚀 Actions</h2>
                <div class="actions-grid">
                    <button id="findThresholdBtn" class="btn btn-info" disabled>
                        🔍 Find Optimal Threshold
                        <small>Free - No API costs</small>
                    </button>

                    <button id="fragmentOnlyBtn" class="btn btn-warning" disabled>
                        ✂️ Fragment Only
                        <small>Free - No AI processing</small>
                    </button>

                    <button id="fullProcessBtn" class="btn btn-danger" disabled>
                        🤖 Full Processing
                        <small>Costs money - Uses AI APIs</small>
                    </button>

                    <button id="continueProcessBtn" class="btn btn-success">
                        ▶️ Process Existing Fragments
                        <small>Costs money - Uses AI APIs</small>
                    </button>
                </div>
            </section>

            <!-- Progress Section -->
            <section class="card">
                <h2>📊 Progress</h2>
                <div id="progressContainer" class="progress-container hidden">
                    <div class="progress-bar">
                        <div id="progressBar" class="progress-fill"></div>
                    </div>
                    <div id="progressText" class="progress-text">Ready to start...</div>
                </div>
                <div id="logContainer" class="log-container">
                    <div id="logOutput" class="log-output"></div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="card">
                <h2>📁 Results</h2>
                <div id="resultsContainer" class="results-container">
                    <p class="no-results">No results yet. Process an audio file to see outputs.</p>
                </div>
            </section>
        </main>

        <!-- Status Bar -->
        <div id="statusBar" class="status-bar">
            <span id="statusText">Ready</span>
            <span id="connectionStatus" class="connection-status">🔴 Not connected</span>
        </div>
    </div>

    <!-- Modal for threshold results -->
    <div id="thresholdModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🔍 Silence Threshold Analysis</h3>
                <button id="closeModal" class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <div id="thresholdResults" class="threshold-results"></div>
            </div>
            <div class="modal-footer">
                <button id="applyThreshold" class="btn btn-primary">Apply Recommended Settings</button>
                <button id="cancelModal" class="btn btn-secondary">Close</button>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>

</html>