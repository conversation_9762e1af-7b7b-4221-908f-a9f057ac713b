class PilgrimAudioBreaker {
    constructor() {
        this.selectedFile = null;
        this.isProcessing = false;
        this.socket = null;
        
        this.initializeElements();
        this.attachEventListeners();
        this.checkServerConnection();
    }

    initializeElements() {
        // File input elements
        this.audioFileInput = document.getElementById('audioFile');
        this.fileInfoDiv = document.getElementById('fileInfo');
        this.fileLabel = document.querySelector('.file-input-label');
        this.fileText = document.querySelector('.file-text');

        // Option elements
        this.maxDurationInput = document.getElementById('maxDuration');
        this.silenceThresholdInput = document.getElementById('silenceThreshold');
        this.minSilenceDurationInput = document.getElementById('minSilenceDuration');
        this.cleanupCheckbox = document.getElementById('cleanup');

        // Button elements
        this.findThresholdBtn = document.getElementById('findThresholdBtn');
        this.fragmentOnlyBtn = document.getElementById('fragmentOnlyBtn');
        this.fullProcessBtn = document.getElementById('fullProcessBtn');
        this.continueProcessBtn = document.getElementById('continueProcessBtn');

        // Progress elements
        this.progressContainer = document.getElementById('progressContainer');
        this.progressBar = document.getElementById('progressBar');
        this.progressText = document.getElementById('progressText');
        this.logOutput = document.getElementById('logOutput');

        // Results elements
        this.resultsContainer = document.getElementById('resultsContainer');

        // Status elements
        this.statusText = document.getElementById('statusText');
        this.connectionStatus = document.getElementById('connectionStatus');

        // Modal elements
        this.thresholdModal = document.getElementById('thresholdModal');
        this.thresholdResults = document.getElementById('thresholdResults');
        this.closeModal = document.getElementById('closeModal');
        this.applyThreshold = document.getElementById('applyThreshold');
        this.cancelModal = document.getElementById('cancelModal');
    }

    attachEventListeners() {
        // File input
        this.audioFileInput.addEventListener('change', (e) => this.handleFileSelect(e));

        // Action buttons
        this.findThresholdBtn.addEventListener('click', () => this.findOptimalThreshold());
        this.fragmentOnlyBtn.addEventListener('click', () => this.fragmentOnly());
        this.fullProcessBtn.addEventListener('click', () => this.fullProcess());
        this.continueProcessBtn.addEventListener('click', () => this.continueProcess());

        // Modal events
        this.closeModal.addEventListener('click', () => this.hideModal());
        this.cancelModal.addEventListener('click', () => this.hideModal());
        this.applyThreshold.addEventListener('click', () => this.applyRecommendedThreshold());

        // Click outside modal to close
        this.thresholdModal.addEventListener('click', (e) => {
            if (e.target === this.thresholdModal) {
                this.hideModal();
            }
        });
    }

    async checkServerConnection() {
        try {
            const response = await fetch('/api/status');
            if (response.ok) {
                const data = await response.json();
                this.updateConnectionStatus(true);
                this.updateStatus(`Connected to server v${data.version}`);
            } else {
                this.updateConnectionStatus(false);
                this.updateStatus('Server connection failed');
            }
        } catch (error) {
            this.updateConnectionStatus(false);
            this.updateStatus('Cannot connect to server');
            console.error('Connection error:', error);
        }
    }

    updateConnectionStatus(connected) {
        if (connected) {
            this.connectionStatus.textContent = '🟢 Connected';
            this.connectionStatus.style.color = '#38a169';
        } else {
            this.connectionStatus.textContent = '🔴 Not connected';
            this.connectionStatus.style.color = '#e53e3e';
        }
    }

    handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            // Validate file type
            const allowedTypes = ['.mp3', '.wav', '.m4a', '.aac', '.flac', '.ogg', '.wma'];
            const fileName = file.name.toLowerCase();
            const isValidType = allowedTypes.some(type => fileName.endsWith(type));

            if (!isValidType) {
                alert('❌ Invalid file type. Please select an audio file (MP3, WAV, M4A, AAC, FLAC, OGG, WMA)');
                event.target.value = ''; // Clear the input
                return;
            }

            // Validate file size (500MB limit)
            const maxSize = 500 * 1024 * 1024; // 500MB
            if (file.size > maxSize) {
                alert('❌ File too large. Maximum size is 500MB.');
                event.target.value = ''; // Clear the input
                return;
            }

            this.selectedFile = file;
            this.updateFileInfo(file);
            this.enableFileBasedButtons();
            this.addLog(`📁 Selected file: ${file.name} (${this.formatFileSize(file.size)})`);
        } else {
            this.selectedFile = null;
            this.hideFileInfo();
            this.disableFileBasedButtons();
        }
    }

    updateFileInfo(file) {
        const fileSize = this.formatFileSize(file.size);
        const fileType = file.type || 'Unknown';
        
        this.fileInfoDiv.innerHTML = `
            <div><strong>📁 File:</strong> ${file.name}</div>
            <div><strong>📊 Size:</strong> ${fileSize}</div>
            <div><strong>🎵 Type:</strong> ${fileType}</div>
        `;
        
        this.fileInfoDiv.classList.remove('hidden');
        this.fileLabel.classList.add('has-file');
        this.fileText.textContent = file.name;
    }

    hideFileInfo() {
        this.fileInfoDiv.classList.add('hidden');
        this.fileLabel.classList.remove('has-file');
        this.fileText.textContent = 'Choose audio file...';
    }

    enableFileBasedButtons() {
        this.findThresholdBtn.disabled = false;
        this.fragmentOnlyBtn.disabled = false;
        this.fullProcessBtn.disabled = false;
    }

    disableFileBasedButtons() {
        this.findThresholdBtn.disabled = true;
        this.fragmentOnlyBtn.disabled = true;
        this.fullProcessBtn.disabled = true;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    getProcessingOptions() {
        return {
            maxDuration: parseInt(this.maxDurationInput.value),
            silenceThreshold: parseInt(this.silenceThresholdInput.value),
            minSilenceDuration: parseFloat(this.minSilenceDurationInput.value),
            cleanup: this.cleanupCheckbox.checked
        };
    }

    updateStatus(message) {
        this.statusText.textContent = message;
    }

    updateProgress(percentage, message) {
        this.progressContainer.classList.remove('hidden');
        this.progressBar.style.width = `${percentage}%`;
        this.progressText.textContent = message;
    }

    addLog(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = `[${timestamp}] ${message}\n`;
        this.logOutput.textContent += logEntry;
        this.logOutput.scrollTop = this.logOutput.scrollHeight;
    }

    clearLog() {
        this.logOutput.textContent = '';
    }

    showModal() {
        this.thresholdModal.classList.remove('hidden');
    }

    hideModal() {
        this.thresholdModal.classList.add('hidden');
    }

    async findOptimalThreshold() {
        if (!this.selectedFile) return;

        this.updateStatus('Finding optimal threshold...');
        this.clearLog();
        this.addLog('🔍 Starting threshold analysis...');

        const formData = new FormData();
        formData.append('audioFile', this.selectedFile);
        formData.append('action', 'findThreshold');

        try {
            const response = await fetch('/api/process', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            this.displayThresholdResults(result.thresholdAnalysis);
            this.showModal();
            this.updateStatus('Threshold analysis complete');
            this.addLog('✅ Threshold analysis completed');

        } catch (error) {
            this.addLog(`❌ Error: ${error.message}`, 'error');
            this.updateStatus('Error during threshold analysis');
        }
    }

    displayThresholdResults(analysis) {
        let html = '<div class="threshold-analysis">';
        
        analysis.forEach(result => {
            const status = result.recommendation;
            const emoji = status === 'GOOD' ? '✅' : 
                         status === 'TOO_MANY' ? '⚠️' : 
                         status === 'FEW' ? '📊' : '❌';
            
            html += `
                <div class="threshold-item">
                    ${emoji} Threshold ${result.threshold}dB, Duration ${result.duration}s: 
                    ${result.silencePoints} silence points
                    ${status === 'GOOD' ? ' (RECOMMENDED)' : ''}
                </div>
            `;
        });
        
        html += '</div>';
        this.thresholdResults.innerHTML = html;
    }

    applyRecommendedThreshold() {
        // Find the first GOOD recommendation and apply it
        // This would be implemented based on the threshold analysis results
        this.hideModal();
        this.addLog('📝 Applied recommended threshold settings');
    }

    async fragmentOnly() {
        if (!this.selectedFile) return;
        await this.processAudio('fragmentOnly');
    }

    async fullProcess() {
        if (!this.selectedFile) return;
        
        const confirmed = confirm(
            '⚠️ This will use AI APIs and cost money. Are you sure you want to continue?'
        );
        
        if (confirmed) {
            await this.processAudio('fullProcess');
        }
    }

    async continueProcess() {
        const confirmed = confirm(
            '⚠️ This will process existing fragments using AI APIs and cost money. Continue?'
        );
        
        if (confirmed) {
            await this.processAudio('continueProcess');
        }
    }

    async processAudio(action) {
        this.isProcessing = true;
        this.updateStatus(`Processing (${action})...`);
        this.clearLog();
        this.updateProgress(0, 'Starting...');

        const formData = new FormData();
        if (this.selectedFile && action !== 'continueProcess') {
            formData.append('audioFile', this.selectedFile);
        }
        formData.append('action', action);
        
        const options = this.getProcessingOptions();
        Object.keys(options).forEach(key => {
            formData.append(key, options[key]);
        });

        try {
            const response = await fetch('/api/process', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Handle streaming response for real-time updates
            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.trim()) {
                        try {
                            const data = JSON.parse(line);
                            this.handleProgressUpdate(data);
                        } catch (e) {
                            // Handle non-JSON log lines
                            this.addLog(line);
                        }
                    }
                }
            }

            this.updateStatus('Processing complete');
            this.updateProgress(100, 'Complete!');

        } catch (error) {
            this.addLog(`❌ Error: ${error.message}`, 'error');
            this.updateStatus('Error during processing');
            this.updateProgress(0, 'Error occurred');
        } finally {
            this.isProcessing = false;
        }
    }

    handleProgressUpdate(data) {
        if (data.type === 'progress') {
            this.updateProgress(data.percentage, data.message);
        } else if (data.type === 'log') {
            this.addLog(data.message);
        } else if (data.type === 'result') {
            this.displayResults(data.results);
        }
    }

    displayResults(results) {
        let html = '';
        
        if (results.fragments && results.fragments.length > 0) {
            html += '<h3>📁 Generated Fragments:</h3>';
            results.fragments.forEach((fragment, index) => {
                html += `
                    <div class="result-item">
                        <span>Fragment ${index + 1}: ${fragment.name}</span>
                        <a href="/download/${fragment.name}" class="download-btn">Download</a>
                    </div>
                `;
            });
        }

        if (results.processed && results.processed.length > 0) {
            html += '<h3>🤖 Processed Files:</h3>';
            results.processed.forEach((file, index) => {
                html += `
                    <div class="result-item">
                        <span>Processed ${index + 1}: ${file.name}</span>
                        <a href="/download/${file.name}" class="download-btn">Download</a>
                    </div>
                `;
            });
        }

        if (results.final) {
            html += '<h3>🎯 Final Output:</h3>';
            html += `
                <div class="result-item">
                    <span><strong>${results.final.name}</strong></span>
                    <a href="/download/${results.final.name}" class="download-btn">Download</a>
                </div>
            `;
        }

        if (html) {
            this.resultsContainer.innerHTML = html;
        }
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new PilgrimAudioBreaker();
});
