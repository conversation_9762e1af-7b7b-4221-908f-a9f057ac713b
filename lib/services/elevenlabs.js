const fetch = require('node-fetch');
const fs = require('fs');
const FormData = require('form-data');
const config = require('../config');

async function processSpeechToSpeech(inputFilePath, outputFilePath) {
  const stsUrl = `https://api.elevenlabs.io/v1/speech-to-speech/${config.elevenlabs.voiceId}`;
  
  const formData = new FormData();
  formData.append('audio', fs.createReadStream(inputFilePath));
  formData.append('model_id', 'eleven_english_sts_v2');
  formData.append('voice_settings', JSON.stringify({
    stability: 0.5,
    similarity_boost: 0.8,
    style: 0.0,
    use_speaker_boost: true
  }));

  try {
    const response = await fetch(stsUrl, {
      method: 'POST',
      headers: {
        ...formData.getHeaders(),
        'xi-api-key': config.elevenlabs.apiKey,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    await fs.promises.writeFile(outputFilePath, Buffer.from(arrayBuffer));
    console.log(`Processed and saved: ${outputFilePath}`);
  } catch (error) {
    console.error('Error:', error.message);
    throw error;
  }
}

module.exports = { processSpeechToSpeech };