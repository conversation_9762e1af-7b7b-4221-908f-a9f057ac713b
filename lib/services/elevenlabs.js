const fetch = require('node-fetch');
const fs = require('fs');
const FormData = require('form-data');
const config = require('../config');

/**
 * Processes speech-to-speech conversion using ElevenLabs API
 * @param {string} inputFilePath - Path to the input audio file
 * @param {string} outputFilePath - Path where the processed audio will be saved
 * @returns {Promise<void>}
 */
async function processSpeechToSpeech(inputFilePath, outputFilePath) {
  try {
    const stsUrl = `https://api.elevenlabs.io/v1/speech-to-speech/${config.elevenlabs.voiceId}`;

    const formData = new FormData();
    formData.append('audio', fs.createReadStream(inputFilePath));
    formData.append('model_id', config.elevenlabs.model);
    formData.append('voice_settings', JSON.stringify(config.elevenlabs.voiceSettings));

    const response = await fetch(stsUrl, {
      method: 'POST',
      headers: {
        ...formData.getHeaders(),
        'xi-api-key': config.elevenlabs.apiKey,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`ElevenLabs API error! status: ${response.status}, message: ${errorText}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    await fs.promises.writeFile(outputFilePath, Buffer.from(arrayBuffer));
    console.log(`Processed and saved: ${outputFilePath}`);
  } catch (error) {
    console.error('Error in ElevenLabs speech-to-speech processing:', error.message);
    throw new Error(`ElevenLabs processing failed: ${error.message}`);
  }
}

/**
 * Validates ElevenLabs configuration and connectivity
 * @returns {Promise<boolean>} True if configuration is valid
 */
async function validateConfiguration() {
  try {
    if (!config.elevenlabs.apiKey || !config.elevenlabs.voiceId) {
      console.error('Missing required ElevenLabs configuration');
      return false;
    }

    // Test API connectivity by fetching voice info
    const response = await fetch(`https://api.elevenlabs.io/v1/voices/${config.elevenlabs.voiceId}`, {
      headers: {
        'xi-api-key': config.elevenlabs.apiKey,
      },
    });

    if (response.ok) {
      const voiceData = await response.json();
      console.log(`Connected to ElevenLabs voice: ${voiceData.name}`);
      return true;
    } else {
      console.error('Failed to connect to ElevenLabs API');
      return false;
    }
  } catch (error) {
    console.error('ElevenLabs configuration validation failed:', error.message);
    return false;
  }
}

module.exports = {
  processSpeechToSpeech,
  validateConfiguration
};