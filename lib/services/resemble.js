const { Resemble } = require('@resemble/node');
const fetch = require('node-fetch');
const fs = require('fs').promises;
const path = require('path');
const config = require('../config');
const server = require('../server');

// Initialize Resemble API
Resemble.setApiKey(config.resemble.apiKey);

/**
 * Processes speech-to-speech conversion using Resemble.ai
 * @param {string} inputFilePath - Path to the input audio file
 * @param {string} outputFilePath - Path where the processed audio will be saved
 * @returns {Promise<void>}
 */
async function processSpeechToSpeech(inputFilePath, outputFilePath) {
  try {
    // Start server to serve the input file
    const baseUrl = await server.startServer();
    const fileName = path.basename(inputFilePath);
    const fileUrl = `${baseUrl}/${fileName}`;
    
    console.log(`File available at: ${fileUrl}`);
    
    // Create the SSML body for Resemble.ai
    const body = `<speak><resemble:convert src="${fileUrl}"/></speak>`;
    
    // Create clip using Resemble.ai API
    const response = await Resemble.v2.clips.createSync(config.resemble.projectUuid, {
      title: path.basename(inputFilePath, '.wav'),
      voice_uuid: config.resemble.voiceUuid,
      body: body,
      is_archived: true,
    });
    
    if (response.success) {
      const clip = response.item;
      const clipURL = clip.audio_src;
      
      // Download the processed audio
      const audioResponse = await fetch(clipURL);
      if (!audioResponse.ok) {
        throw new Error(`Failed to download audio: ${audioResponse.status} ${audioResponse.statusText}`);
      }
      
      const arrayBuffer = await audioResponse.arrayBuffer();
      await fs.writeFile(outputFilePath, Buffer.from(arrayBuffer));
      console.log(`Processed and saved: ${outputFilePath}`);
    } else {
      throw new Error(`Resemble.ai response was unsuccessful: ${JSON.stringify(response)}`);
    }
  } catch (error) {
    console.error('Error in Resemble speech-to-speech processing:', error.message);
    throw new Error(`Resemble processing failed: ${error.message}`);
  }
}

/**
 * Validates Resemble.ai configuration and connectivity
 * @returns {Promise<boolean>} True if configuration is valid
 */
async function validateConfiguration() {
  try {
    if (!config.resemble.apiKey || !config.resemble.projectUuid || !config.resemble.voiceUuid) {
      console.error('Missing required Resemble.ai configuration');
      return false;
    }
    
    // Test API connectivity by fetching project info
    const response = await Resemble.v2.projects.get(config.resemble.projectUuid);
    if (response.success) {
      console.log(`Connected to Resemble.ai project: ${response.item.name}`);
      return true;
    } else {
      console.error('Failed to connect to Resemble.ai project');
      return false;
    }
  } catch (error) {
    console.error('Resemble.ai configuration validation failed:', error.message);
    return false;
  }
}

module.exports = { 
  processSpeechToSpeech, 
  validateConfiguration 
};
