const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const util = require('util');

const execPromise = util.promisify(exec);

async function findWavFragments() {
  const files = await fs.readdir('.');
  return files.filter(file => file.startsWith('output_fragment') && file.endsWith('.wav'))
              .sort((a, b) => {
                const numA = parseInt(a.match(/\d+/)[0]);
                const numB = parseInt(b.match(/\d+/)[0]);
                return numA - numB;
              });
}

async function combineAudioFiles(inputDir, outputFile) {
  const files = await fs.readdir(inputDir);
  const sortedFiles = files.filter(file => file.startsWith('processed_') && file.endsWith('.mp3'))
                         .sort((a, b) => {
                           const numA = parseInt(a.match(/\d+/)[0]);
                           const numB = parseInt(b.match(/\d+/)[0]);
                           return numA - numB;
                         });
  
  const fileList = sortedFiles.map(file => path.join(inputDir, file)).join('|');
  const command = `ffmpeg -i "concat:${fileList}" -acodec copy ${outputFile}`;
  
  try {
    await execPromise(command);
    console.log(`Combined audio saved as ${outputFile}`);
  } catch (error) {
    console.error('Error combining audio files:', error);
    throw error;
  }
}

module.exports = { findWavFragments, combineAudioFiles };