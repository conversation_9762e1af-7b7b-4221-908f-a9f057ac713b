const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const util = require('util');

const execPromise = util.promisify(exec);

/**
 * Finds and sorts WAV fragment files in the current directory
 * @param {string} [directory='.'] - Directory to search for fragments
 * @param {string} [prefix='output_fragment'] - File prefix to filter by
 * @returns {Promise<string[]>} Sorted array of fragment file paths
 */
async function findWavFragments(directory = '.', prefix = 'output_fragment') {
  try {
    const files = await fs.readdir(directory);
    return files
      .filter(file => file.startsWith(prefix) && file.endsWith('.wav'))
      .sort((a, b) => {
        const numA = parseInt(a.match(/\d+/)?.[0] || '0');
        const numB = parseInt(b.match(/\d+/)?.[0] || '0');
        return numA - numB;
      })
      .map(file => path.join(directory, file));
  } catch (error) {
    console.error(`Error finding WAV fragments in ${directory}:`, error.message);
    throw new Error(`Failed to find WAV fragments: ${error.message}`);
  }
}

/**
 * Combines multiple audio files into a single output file
 * @param {string} inputDir - Directory containing processed audio files
 * @param {string} outputFile - Path for the combined output file
 * @param {string} [prefix='processed_'] - File prefix to filter by
 * @returns {Promise<void>}
 */
async function combineAudioFiles(inputDir, outputFile, prefix = 'processed_') {
  try {
    const files = await fs.readdir(inputDir);
    const sortedFiles = files
      .filter(file => file.startsWith(prefix) && file.endsWith('.mp3'))
      .sort((a, b) => {
        const numA = parseInt(a.match(/\d+/)?.[0] || '0');
        const numB = parseInt(b.match(/\d+/)?.[0] || '0');
        return numA - numB;
      });

    if (sortedFiles.length === 0) {
      throw new Error(`No ${prefix}*.mp3 files found in ${inputDir}`);
    }

    const fileList = sortedFiles.map(file => path.join(inputDir, file)).join('|');
    const command = `ffmpeg -i "concat:${fileList}" -acodec copy "${outputFile}"`;

    console.log(`Combining ${sortedFiles.length} audio files...`);
    await execPromise(command);
    console.log(`Combined audio saved as ${outputFile}`);
  } catch (error) {
    console.error('Error combining audio files:', error.message);
    throw new Error(`Failed to combine audio files: ${error.message}`);
  }
}

/**
 * Detects silence points in an audio file with adaptive thresholds
 * @param {string} inputFile - Path to the input audio file
 * @param {number} silenceThreshold - Initial silence threshold in dB
 * @param {number} minSilenceDuration - Minimum silence duration in seconds
 * @returns {Promise<number[]>} Array of silence start times in seconds
 */
async function detectSilencePoints(inputFile, silenceThreshold = -40, minSilenceDuration = 0.3) {
  const thresholds = [silenceThreshold, -45, -50, -55, -35, -30]; // Try more sensitive thresholds first
  const durations = [minSilenceDuration, 0.2, 0.1]; // Try shorter silence durations

  for (const threshold of thresholds) {
    for (const duration of durations) {
      try {
        console.log(`Trying silence detection with threshold ${threshold}dB and duration ${duration}s...`);

        // Use FFmpeg's silencedetect filter to find silence points
        const silenceCommand = `ffmpeg -i "${inputFile}" -af silencedetect=noise=${threshold}dB:d=${duration} -f null - 2>&1`;

        const { stdout, stderr } = await execPromise(silenceCommand);
        const output = stdout + stderr; // Combine both outputs

        // Parse silence detection output
        const silencePoints = [];
        const silenceRegex = /silence_start: ([\d.]+)/g;
        let match;

        while ((match = silenceRegex.exec(output)) !== null) {
          const silenceStart = parseFloat(match[1]);
          silencePoints.push(silenceStart);
        }

        console.log(`Found ${silencePoints.length} silence points with threshold ${threshold}dB`);

        // If we found a reasonable number of silence points, use them
        if (silencePoints.length >= 2 && silencePoints.length <= 50) {
          console.log(`Using ${silencePoints.length} silence points for optimal fragmentation`);
          return silencePoints.sort((a, b) => a - b);
        }

        // If we found too many, try next threshold (less sensitive)
        if (silencePoints.length > 50) {
          console.log(`Too many silence points (${silencePoints.length}), trying less sensitive threshold...`);
          continue;
        }

        // If we found some but not many, continue to try more sensitive settings
        if (silencePoints.length > 0) {
          console.log(`Found ${silencePoints.length} silence points, trying more sensitive settings...`);
        }

      } catch (error) {
        console.warn(`Silence detection failed with threshold ${threshold}dB: ${error.message}`);
        continue;
      }
    }
  }

  console.warn('Could not find suitable silence points with any threshold, falling back to time-based splitting');
  return [];
}

/**
 * Calculates optimal break points based on silence detection and target duration
 * @param {number[]} silencePoints - Array of silence start times
 * @param {number} totalDuration - Total audio duration in seconds
 * @param {number} maxDuration - Target maximum duration per fragment
 * @returns {number[]} Array of optimal break points in seconds
 */
function calculateOptimalBreakPoints(silencePoints, totalDuration, maxDuration) {
  const breakPoints = [];
  let currentPosition = 0;

  while (currentPosition + maxDuration < totalDuration) {
    const targetBreakPoint = currentPosition + maxDuration;

    // Find the best silence point near the target break point
    const optimalBreakPoint = findNearestSilencePoint(silencePoints, targetBreakPoint, maxDuration * 0.1); // Allow 10% variance

    if (optimalBreakPoint !== null) {
      breakPoints.push(optimalBreakPoint);
      currentPosition = optimalBreakPoint;
      console.log(`Optimal break at ${optimalBreakPoint.toFixed(1)}s (target was ${targetBreakPoint.toFixed(1)}s)`);
    } else {
      // No suitable silence point found, use time-based break
      breakPoints.push(targetBreakPoint);
      currentPosition = targetBreakPoint;
      console.log(`Time-based break at ${targetBreakPoint.toFixed(1)}s (no suitable silence found)`);
    }
  }

  return breakPoints;
}

/**
 * Finds the nearest silence point to a target time within a tolerance
 * @param {number[]} silencePoints - Array of silence start times
 * @param {number} targetTime - Target time to find silence near
 * @param {number} tolerance - Maximum distance from target time
 * @returns {number|null} Nearest silence point or null if none found
 */
function findNearestSilencePoint(silencePoints, targetTime, tolerance) {
  let bestPoint = null;
  let bestDistance = Infinity;

  for (const silencePoint of silencePoints) {
    const distance = Math.abs(silencePoint - targetTime);

    if (distance <= tolerance && distance < bestDistance) {
      bestPoint = silencePoint;
      bestDistance = distance;
    }
  }

  return bestPoint;
}

/**
 * Creates audio fragments at specified break points
 * @param {string} inputFile - Path to the input audio file
 * @param {string} outputPrefix - Prefix for output fragment files
 * @param {number[]} breakPoints - Array of break points in seconds
 * @param {number} totalDuration - Total audio duration
 * @returns {Promise<string[]>} Array of created fragment file paths
 */
async function createFragmentsAtBreakPoints(inputFile, outputPrefix, breakPoints, totalDuration) {
  const fragments = [];
  let startTime = 0;

  // Create fragments between break points
  for (let i = 0; i <= breakPoints.length; i++) {
    const endTime = i < breakPoints.length ? breakPoints[i] : totalDuration;
    const duration = endTime - startTime;

    if (duration > 1) { // Only create fragments longer than 1 second
      const outputFile = `${outputPrefix}_${i}.wav`;

      const fragmentCommand = `ffmpeg -i "${inputFile}" -ss ${startTime} -t ${duration} -acodec pcm_s16le -ar 44100 -ac 2 "${outputFile}" -y`;

      await execPromise(fragmentCommand);
      fragments.push(outputFile);

      console.log(`Created fragment ${i + 1}: ${outputFile} (${startTime.toFixed(1)}s - ${endTime.toFixed(1)}s, duration: ${duration.toFixed(1)}s)`);
    }

    startTime = endTime;
  }

  return fragments;
}

/**
 * Splits an audio file into fragments based on duration and silence detection
 * @param {string} inputFile - Path to the input audio file
 * @param {string} outputPrefix - Prefix for output fragment files
 * @param {Object} options - Fragmentation options
 * @param {number} [options.maxDuration=240] - Maximum duration per fragment in seconds
 * @param {number} [options.silenceThreshold=-30] - Silence threshold in dB
 * @param {number} [options.minSilenceDuration=0.5] - Minimum silence duration to split on
 * @returns {Promise<string[]>} Array of created fragment file paths
 */
async function fragmentAudioFile(inputFile, outputPrefix = 'output_fragment', options = {}) {
  const {
    maxDuration = 240, // 4 minutes
    silenceThreshold = -30, // dB
    minSilenceDuration = 0.5 // seconds
  } = options;

  try {
    // Check if input file exists
    await fs.access(inputFile);

    // Get audio duration first
    const durationCommand = `ffprobe -v quiet -show_entries format=duration -of csv=p=0 "${inputFile}"`;
    const { stdout: durationOutput } = await execPromise(durationCommand);
    const totalDuration = parseFloat(durationOutput.trim());

    if (isNaN(totalDuration) || totalDuration <= 0) {
      throw new Error('Could not determine audio duration');
    }

    console.log(`Input file duration: ${totalDuration.toFixed(2)} seconds`);

    // If file is shorter than max duration, just convert to WAV
    if (totalDuration <= maxDuration) {
      const outputFile = `${outputPrefix}_1.wav`;
      const convertCommand = `ffmpeg -i "${inputFile}" -acodec pcm_s16le -ar 44100 -ac 2 "${outputFile}" -y`;
      await execPromise(convertCommand);
      console.log(`Created single fragment: ${outputFile}`);
      return [outputFile];
    }

    // Step 1: Detect silence points in the audio
    console.log('Analyzing audio for optimal break points...');
    const silencePoints = await detectSilencePoints(inputFile, silenceThreshold, minSilenceDuration);

    // Step 2: Calculate optimal break points based on target duration and silence
    const breakPoints = calculateOptimalBreakPoints(silencePoints, totalDuration, maxDuration);

    console.log(`Found ${breakPoints.length} optimal break points at: ${breakPoints.map(p => `${p.toFixed(1)}s`).join(', ')}`);

    // Step 3: Create fragments using the calculated break points
    const fragments = await createFragmentsAtBreakPoints(inputFile, outputPrefix, breakPoints, totalDuration);

    console.log(`Created ${fragments.length} fragments with natural speech breaks`);
    fragments.forEach((fragment, index) => {
      console.log(`  Fragment ${index + 1}: ${path.basename(fragment)}`);
    });

    return fragments;

  } catch (error) {
    console.error('Error fragmenting audio file:', error.message);
    throw new Error(`Failed to fragment audio file: ${error.message}`);
  }
}

/**
 * Gets audio file information
 * @param {string} filePath - Path to the audio file
 * @returns {Promise<Object>} Audio file information
 */
async function getAudioInfo(filePath) {
  try {
    const command = `ffprobe -v quiet -print_format json -show_format -show_streams "${filePath}"`;
    const { stdout } = await execPromise(command);
    const info = JSON.parse(stdout);

    const audioStream = info.streams.find(stream => stream.codec_type === 'audio');
    if (!audioStream) {
      throw new Error('No audio stream found in file');
    }

    return {
      duration: parseFloat(info.format.duration),
      bitRate: parseInt(info.format.bit_rate),
      sampleRate: parseInt(audioStream.sample_rate),
      channels: parseInt(audioStream.channels),
      codec: audioStream.codec_name,
      size: parseInt(info.format.size)
    };
  } catch (error) {
    console.error('Error getting audio info:', error.message);
    throw new Error(`Failed to get audio info: ${error.message}`);
  }
}

/**
 * Validates that required audio processing tools are available
 * @returns {Promise<boolean>} True if all tools are available
 */
async function validateAudioTools() {
  try {
    await execPromise('ffmpeg -version');
    await execPromise('ffprobe -version');
    return true;
  } catch (error) {
    console.error('FFmpeg/FFprobe is not available. Please install FFmpeg to process audio files.');
    return false;
  }
}

module.exports = {
  findWavFragments,
  combineAudioFiles,
  fragmentAudioFile,
  getAudioInfo,
  validateAudioTools
};