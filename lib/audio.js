const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const util = require('util');

const execPromise = util.promisify(exec);

/**
 * Finds and sorts WAV fragment files in the current directory
 * @param {string} [directory='.'] - Directory to search for fragments
 * @param {string} [prefix='output_fragment'] - File prefix to filter by
 * @returns {Promise<string[]>} Sorted array of fragment file paths
 */
async function findWavFragments(directory = '.', prefix = 'output_fragment') {
  try {
    const files = await fs.readdir(directory);
    return files
      .filter(file => file.startsWith(prefix) && file.endsWith('.wav'))
      .sort((a, b) => {
        const numA = parseInt(a.match(/\d+/)?.[0] || '0');
        const numB = parseInt(b.match(/\d+/)?.[0] || '0');
        return numA - numB;
      })
      .map(file => path.join(directory, file));
  } catch (error) {
    console.error(`Error finding WAV fragments in ${directory}:`, error.message);
    throw new Error(`Failed to find WAV fragments: ${error.message}`);
  }
}

/**
 * Combines multiple audio files into a single output file
 * @param {string} inputDir - Directory containing processed audio files
 * @param {string} outputFile - Path for the combined output file
 * @param {string} [prefix='processed_'] - File prefix to filter by
 * @returns {Promise<void>}
 */
async function combineAudioFiles(inputDir, outputFile, prefix = 'processed_') {
  try {
    const files = await fs.readdir(inputDir);
    const sortedFiles = files
      .filter(file => file.startsWith(prefix) && file.endsWith('.mp3'))
      .sort((a, b) => {
        const numA = parseInt(a.match(/\d+/)?.[0] || '0');
        const numB = parseInt(b.match(/\d+/)?.[0] || '0');
        return numA - numB;
      });

    if (sortedFiles.length === 0) {
      throw new Error(`No ${prefix}*.mp3 files found in ${inputDir}`);
    }

    const fileList = sortedFiles.map(file => path.join(inputDir, file)).join('|');
    const command = `ffmpeg -i "concat:${fileList}" -acodec copy "${outputFile}"`;

    console.log(`Combining ${sortedFiles.length} audio files...`);
    await execPromise(command);
    console.log(`Combined audio saved as ${outputFile}`);
  } catch (error) {
    console.error('Error combining audio files:', error.message);
    throw new Error(`Failed to combine audio files: ${error.message}`);
  }
}

/**
 * Validates that required audio processing tools are available
 * @returns {Promise<boolean>} True if all tools are available
 */
async function validateAudioTools() {
  try {
    await execPromise('ffmpeg -version');
    return true;
  } catch (error) {
    console.error('FFmpeg is not available. Please install FFmpeg to process audio files.');
    return false;
  }
}

module.exports = {
  findWavFragments,
  combineAudioFiles,
  validateAudioTools
};