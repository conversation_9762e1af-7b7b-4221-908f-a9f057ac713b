const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const util = require('util');

const execPromise = util.promisify(exec);

/**
 * Finds and sorts WAV fragment files in the current directory
 * @param {string} [directory='.'] - Directory to search for fragments
 * @param {string} [prefix='output_fragment'] - File prefix to filter by
 * @returns {Promise<string[]>} Sorted array of fragment file paths
 */
async function findWavFragments(directory = '.', prefix = 'output_fragment') {
  try {
    const files = await fs.readdir(directory);
    return files
      .filter(file => file.startsWith(prefix) && file.endsWith('.wav'))
      .sort((a, b) => {
        const numA = parseInt(a.match(/\d+/)?.[0] || '0');
        const numB = parseInt(b.match(/\d+/)?.[0] || '0');
        return numA - numB;
      })
      .map(file => path.join(directory, file));
  } catch (error) {
    console.error(`Error finding WAV fragments in ${directory}:`, error.message);
    throw new Error(`Failed to find WAV fragments: ${error.message}`);
  }
}

/**
 * Combines multiple audio files into a single output file
 * @param {string} inputDir - Directory containing processed audio files
 * @param {string} outputFile - Path for the combined output file
 * @param {string} [prefix='processed_'] - File prefix to filter by
 * @returns {Promise<void>}
 */
async function combineAudioFiles(inputDir, outputFile, prefix = 'processed_') {
  try {
    const files = await fs.readdir(inputDir);
    const sortedFiles = files
      .filter(file => file.startsWith(prefix) && file.endsWith('.mp3'))
      .sort((a, b) => {
        const numA = parseInt(a.match(/\d+/)?.[0] || '0');
        const numB = parseInt(b.match(/\d+/)?.[0] || '0');
        return numA - numB;
      });

    if (sortedFiles.length === 0) {
      throw new Error(`No ${prefix}*.mp3 files found in ${inputDir}`);
    }

    const fileList = sortedFiles.map(file => path.join(inputDir, file)).join('|');
    const command = `ffmpeg -i "concat:${fileList}" -acodec copy "${outputFile}"`;

    console.log(`Combining ${sortedFiles.length} audio files...`);
    await execPromise(command);
    console.log(`Combined audio saved as ${outputFile}`);
  } catch (error) {
    console.error('Error combining audio files:', error.message);
    throw new Error(`Failed to combine audio files: ${error.message}`);
  }
}

/**
 * Splits an audio file into fragments based on duration and silence detection
 * @param {string} inputFile - Path to the input audio file
 * @param {string} outputPrefix - Prefix for output fragment files
 * @param {Object} options - Fragmentation options
 * @param {number} [options.maxDuration=240] - Maximum duration per fragment in seconds
 * @param {number} [options.silenceThreshold=-30] - Silence threshold in dB
 * @param {number} [options.minSilenceDuration=0.5] - Minimum silence duration to split on
 * @returns {Promise<string[]>} Array of created fragment file paths
 */
async function fragmentAudioFile(inputFile, outputPrefix = 'output_fragment', options = {}) {
  const {
    maxDuration = 240, // 4 minutes
    silenceThreshold = -30, // dB
    minSilenceDuration = 0.5 // seconds
  } = options;

  try {
    // Check if input file exists
    await fs.access(inputFile);

    // Get audio duration first
    const durationCommand = `ffprobe -v quiet -show_entries format=duration -of csv=p=0 "${inputFile}"`;
    const { stdout: durationOutput } = await execPromise(durationCommand);
    const totalDuration = parseFloat(durationOutput.trim());

    if (isNaN(totalDuration) || totalDuration <= 0) {
      throw new Error('Could not determine audio duration');
    }

    console.log(`Input file duration: ${totalDuration.toFixed(2)} seconds`);

    // If file is shorter than max duration, just convert to WAV
    if (totalDuration <= maxDuration) {
      const outputFile = `${outputPrefix}_1.wav`;
      const convertCommand = `ffmpeg -i "${inputFile}" -acodec pcm_s16le -ar 44100 -ac 2 "${outputFile}" -y`;
      await execPromise(convertCommand);
      console.log(`Created single fragment: ${outputFile}`);
      return [outputFile];
    }

    // Create fragments using FFmpeg's segment filter with silence detection
    const segmentDuration = maxDuration;
    const outputPattern = `${outputPrefix}_%d.wav`;

    // Use FFmpeg to split on silence or duration
    const segmentCommand = `ffmpeg -i "${inputFile}" ` +
      `-f segment ` +
      `-segment_time ${segmentDuration} ` +
      `-segment_format wav ` +
      `-acodec pcm_s16le ` +
      `-ar 44100 ` +
      `-ac 2 ` +
      `-reset_timestamps 1 ` +
      `"${outputPattern}" -y`;

    console.log('Fragmenting audio file...');
    await execPromise(segmentCommand);

    // Find all created fragments
    const fragments = await findWavFragments('.', outputPrefix.split('/').pop());

    console.log(`Created ${fragments.length} fragments`);
    fragments.forEach((fragment, index) => {
      console.log(`  Fragment ${index + 1}: ${path.basename(fragment)}`);
    });

    return fragments;

  } catch (error) {
    console.error('Error fragmenting audio file:', error.message);
    throw new Error(`Failed to fragment audio file: ${error.message}`);
  }
}

/**
 * Gets audio file information
 * @param {string} filePath - Path to the audio file
 * @returns {Promise<Object>} Audio file information
 */
async function getAudioInfo(filePath) {
  try {
    const command = `ffprobe -v quiet -print_format json -show_format -show_streams "${filePath}"`;
    const { stdout } = await execPromise(command);
    const info = JSON.parse(stdout);

    const audioStream = info.streams.find(stream => stream.codec_type === 'audio');
    if (!audioStream) {
      throw new Error('No audio stream found in file');
    }

    return {
      duration: parseFloat(info.format.duration),
      bitRate: parseInt(info.format.bit_rate),
      sampleRate: parseInt(audioStream.sample_rate),
      channels: parseInt(audioStream.channels),
      codec: audioStream.codec_name,
      size: parseInt(info.format.size)
    };
  } catch (error) {
    console.error('Error getting audio info:', error.message);
    throw new Error(`Failed to get audio info: ${error.message}`);
  }
}

/**
 * Validates that required audio processing tools are available
 * @returns {Promise<boolean>} True if all tools are available
 */
async function validateAudioTools() {
  try {
    await execPromise('ffmpeg -version');
    await execPromise('ffprobe -version');
    return true;
  } catch (error) {
    console.error('FFmpeg/FFprobe is not available. Please install FFmpeg to process audio files.');
    return false;
  }
}

module.exports = {
  findWavFragments,
  combineAudioFiles,
  fragmentAudioFile,
  getAudioInfo,
  validateAudioTools
};