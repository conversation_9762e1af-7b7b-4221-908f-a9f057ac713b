import { NextApiRequest } from 'next';

const ELEVENLABS_VOICE_ID = process.env.ELEVENLABS_VOICE_ID;
const XI_API_KEY = process.env.ELEVENLABS_API_KEY;

export async function convertSpeech(audioFile: Blob): Promise<Blob> {
  const stsUrl = `https://api.elevenlabs.io/v1/speech-to-speech/${ELEVENLABS_VOICE_ID}`;
  
  const formData = new FormData();
  formData.append('audio', audioFile);
  formData.append('model_id', 'eleven_english_sts_v2');
  formData.append('voice_settings', JSON.stringify({
    stability: 0.5,
    similarity_boost: 0.8,
    style: 0.0,
    use_speaker_boost: true
  }));

  try {
    const response = await fetch(stsUrl, {
      method: 'POST',
      headers: {
        'xi-api-key': XI_API_KEY!,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    return new Blob([arrayBuffer], { type: 'audio/mpeg' });
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
}