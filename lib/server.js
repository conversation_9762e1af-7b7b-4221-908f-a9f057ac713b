const httpServer = require('http-server');
const getPort = require('get-port');
const ngrok = require('ngrok');

let server;
let ngrokUrl;

async function startServer() {
  const port = await getPort();
  server = httpServer.createServer({ root: '.' });
  server.listen(port);
  ngrokUrl = await ngrok.connect(port);
  console.log(`Local server started at ${ngrokUrl}`);
  return ngrokUrl;
}

async function stopServer() {
  if (server) {
    server.close();
  }
  await ngrok.disconnect();
  await ngrok.kill();
}

module.exports = { startServer, stopServer };