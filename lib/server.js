const http = require('http');
const fs = require('fs');
const path = require('path');

let server;
let serverUrl;

/**
 * Starts a simple HTTP server to serve audio files
 * @param {number} [port=3000] - Port to start the server on
 * @returns {Promise<string>} Server URL
 */
async function startServer(port = 3000) {
  return new Promise((resolve, reject) => {
    try {
      server = http.createServer((req, res) => {
        // Enable CORS
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

        // Parse the URL to get the file path
        const filePath = path.join('.', req.url);

        // Check if file exists and serve it
        fs.readFile(filePath, (err, data) => {
          if (err) {
            res.writeHead(404, { 'Content-Type': 'text/plain' });
            res.end('File not found');
            return;
          }

          // Set appropriate content type based on file extension
          const ext = path.extname(filePath).toLowerCase();
          const contentType = ext === '.wav' ? 'audio/wav' :
                             ext === '.mp3' ? 'audio/mpeg' :
                             'application/octet-stream';

          res.writeHead(200, { 'Content-Type': contentType });
          res.end(data);
        });
      });

      server.listen(port, (err) => {
        if (err) {
          reject(new Error(`Failed to start server: ${err.message}`));
          return;
        }

        serverUrl = `http://localhost:${port}`;
        console.log(`Local server started at ${serverUrl}`);
        resolve(serverUrl);
      });

      server.on('error', (err) => {
        if (err.code === 'EADDRINUSE') {
          // Try next port
          startServer(port + 1).then(resolve).catch(reject);
        } else {
          reject(err);
        }
      });
    } catch (error) {
      reject(new Error(`Server startup failed: ${error.message}`));
    }
  });
}

/**
 * Stops the HTTP server
 * @returns {Promise<void>}
 */
async function stopServer() {
  return new Promise((resolve) => {
    if (server) {
      server.close(() => {
        console.log('Server stopped');
        server = null;
        serverUrl = null;
        resolve();
      });
    } else {
      resolve();
    }
  });
}

/**
 * Gets the current server URL
 * @returns {string|null} Current server URL or null if not running
 */
function getServerUrl() {
  return serverUrl;
}

module.exports = {
  startServer,
  stopServer,
  getServerUrl
};