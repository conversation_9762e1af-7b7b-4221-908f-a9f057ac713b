/**
 * Configuration settings for the audio processing application
 * Environment variables take precedence over default values
 */

// Load environment variables from .env file if available
try {
  require('dotenv').config();
} catch (error) {
  // dotenv is optional, continue without it
}

const config = {
  elevenlabs: {
    apiKey: process.env.ELEVENLABS_API_KEY || "********************************",
    voiceId: process.env.ELEVENLABS_VOICE_ID || "m8zh0MPgCw36cExKQqkX",
    model: process.env.ELEVENLABS_MODEL || "eleven_english_sts_v2",
    voiceSettings: {
      stability: parseFloat(process.env.ELEVENLABS_STABILITY || "0.5"),
      similarity_boost: parseFloat(process.env.ELEVENLABS_SIMILARITY_BOOST || "0.8"),
      style: parseFloat(process.env.ELEVENLABS_STYLE || "0.0"),
      use_speaker_boost: process.env.ELEVENLABS_SPEAKER_BOOST !== "false"
    }
  },
  resemble: {
    apiKey: process.env.RESEMBLE_API_KEY || "VVbhSXUSfGNuSYRKv8AqoAtt",
    projectUuid: process.env.RESEMBLE_PROJECT_UUID || "42c8c233",
    voiceUuid: process.env.RESEMBLE_VOICE_UUID || "9de11312"
  },
  audio: {
    inputDirectory: process.env.INPUT_DIRECTORY || ".",
    outputDirectory: process.env.OUTPUT_DIRECTORY || "processed",
    finalOutputFile: process.env.FINAL_OUTPUT_FILE || "final_output.mp3",
    fragmentPrefix: process.env.FRAGMENT_PREFIX || "output_fragment",
    processedPrefix: process.env.PROCESSED_PREFIX || "processed_",
    // Fragmentation settings
    maxFragmentDuration: parseInt(process.env.MAX_FRAGMENT_DURATION || "240"), // 4 minutes
    silenceThreshold: parseInt(process.env.SILENCE_THRESHOLD || "-30"), // dB
    minSilenceDuration: parseFloat(process.env.MIN_SILENCE_DURATION || "0.5") // seconds
  },
  server: {
    port: parseInt(process.env.SERVER_PORT || "3000"),
    host: process.env.SERVER_HOST || "localhost"
  },
  // Default service to use: "resemble" or "elevenlabs"
  defaultService: process.env.DEFAULT_SERVICE || "resemble"
};

/**
 * Validates the configuration
 * @returns {Object} Validation result with isValid boolean and errors array
 */
function validateConfig() {
  const errors = [];

  if (config.defaultService === 'elevenlabs') {
    if (!config.elevenlabs.apiKey) {
      errors.push('ElevenLabs API key is required');
    }
    if (!config.elevenlabs.voiceId) {
      errors.push('ElevenLabs voice ID is required');
    }
  } else if (config.defaultService === 'resemble') {
    if (!config.resemble.apiKey) {
      errors.push('Resemble API key is required');
    }
    if (!config.resemble.projectUuid) {
      errors.push('Resemble project UUID is required');
    }
    if (!config.resemble.voiceUuid) {
      errors.push('Resemble voice UUID is required');
    }
  } else {
    errors.push('Invalid default service. Must be "elevenlabs" or "resemble"');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

module.exports = {
  ...config,
  validateConfig
};