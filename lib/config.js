// Configuration settings
module.exports = {
  elevenlabs: {
    apiKey: process.env.ELEVENLABS_API_KEY || "********************************",
    voiceId: process.env.ELEVENLABS_VOICE_ID || "m8zh0MPgCw36cExKQqkX"
  },
  resemble: {
    apiKey: process.env.RESEMBLE_API_KEY || "VVbhSXUSfGNuSYRKv8AqoAtt",
    projectUuid: process.env.RESEMBLE_PROJECT_UUID || "42c8c233",
    voiceUuid: process.env.RESEMBLE_VOICE_UUID || "9de11312"
  },
  output: {
    directory: "processed",
    finalFile: "final_output.mp3"
  },
  // Default service to use
  defaultService: "resemble" // or "elevenlabs"
}