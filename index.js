const fs = require('fs').promises;
const path = require('path');
const config = require('./lib/config');
const elevenlabs = require('./lib/services/elevenlabs');
const resemble = require('./lib/services/resemble');
const server = require('./lib/server');
const audio = require('./lib/audio');

async function processAllFragments(fragmentPaths) {
  await fs.mkdir(config.output.directory, { recursive: true });
  
  const service = config.defaultService === 'resemble' ? resemble : elevenlabs;
  
  for (let i = 0; i < fragmentPaths.length; i++) {
    const inputPath = fragmentPaths[i];
    const outputPath = path.join(config.output.directory, `processed_${i + 1}.mp3`);
    console.log(`Processing fragment ${i + 1}...`);
    await service.processSpeechToSpeech(inputPath, outputPath);
  }
}

async function main() {
  try {
    const fragments = await audio.findWavFragments();
    console.log('Found fragments:', fragments);

    await processAllFragments(fragments);
    console.log('All fragments processed.');

    await audio.combineAudioFiles(config.output.directory, config.output.finalFile);
    console.log('Process completed successfully.');
  } catch (error) {
    console.error('An error occurred:', error);
  } finally {
    if (config.defaultService === 'resemble') {
      await server.stopServer();
    }
  }
}

main();
