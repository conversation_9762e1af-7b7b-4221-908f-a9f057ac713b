const fs = require('fs').promises;
const path = require('path');
const config = require('./lib/config');
const elevenlabs = require('./lib/services/elevenlabs');
const resemble = require('./lib/services/resemble');
const server = require('./lib/server');
const audio = require('./lib/audio');

/**
 * Processes all audio fragments using the configured service
 * @param {string[]} fragmentPaths - Array of fragment file paths
 * @returns {Promise<void>}
 */
async function processAllFragments(fragmentPaths) {
  if (!fragmentPaths || fragmentPaths.length === 0) {
    throw new Error('No fragments provided for processing');
  }

  // Create output directory
  await fs.mkdir(config.audio.outputDirectory, { recursive: true });

  // Select service based on configuration
  const service = config.defaultService === 'resemble' ? resemble : elevenlabs;

  console.log(`Processing ${fragmentPaths.length} fragments using ${config.defaultService}...`);

  for (let i = 0; i < fragmentPaths.length; i++) {
    const inputPath = fragmentPaths[i];
    const outputPath = path.join(config.audio.outputDirectory, `${config.audio.processedPrefix}${i + 1}.mp3`);

    console.log(`Processing fragment ${i + 1}/${fragmentPaths.length}: ${path.basename(inputPath)}`);

    try {
      await service.processSpeechToSpeech(inputPath, outputPath);
    } catch (error) {
      console.error(`Failed to process fragment ${i + 1}:`, error.message);
      throw new Error(`Fragment processing failed at ${i + 1}/${fragmentPaths.length}: ${error.message}`);
    }
  }
}

/**
 * Validates the environment and configuration before processing
 * @returns {Promise<void>}
 */
async function validateEnvironment() {
  console.log('Validating environment...');

  // Validate configuration
  const configValidation = config.validateConfig();
  if (!configValidation.isValid) {
    throw new Error(`Configuration validation failed:\n${configValidation.errors.join('\n')}`);
  }

  // Validate audio tools
  const audioToolsValid = await audio.validateAudioTools();
  if (!audioToolsValid) {
    throw new Error('Required audio processing tools are not available');
  }

  // Validate service configuration
  const service = config.defaultService === 'resemble' ? resemble : elevenlabs;
  const serviceValid = await service.validateConfiguration();
  if (!serviceValid) {
    throw new Error(`${config.defaultService} service configuration is invalid`);
  }

  console.log('Environment validation completed successfully');
}

/**
 * Main application function
 */
async function main() {
  console.log('=== Pilgrim Audio Breaker ===');
  console.log(`Using ${config.defaultService} service for audio processing`);

  try {
    // Validate environment
    await validateEnvironment();

    // Find audio fragments
    console.log(`Searching for fragments in: ${config.audio.inputDirectory}`);
    const fragments = await audio.findWavFragments(
      config.audio.inputDirectory,
      config.audio.fragmentPrefix
    );

    if (fragments.length === 0) {
      console.log('No audio fragments found. Please ensure fragment files exist.');
      return;
    }

    console.log(`Found ${fragments.length} fragments:`, fragments.map(f => path.basename(f)));

    // Process all fragments
    await processAllFragments(fragments);
    console.log('All fragments processed successfully.');

    // Combine processed files
    console.log('Combining processed audio files...');
    await audio.combineAudioFiles(
      config.audio.outputDirectory,
      config.audio.finalOutputFile,
      config.audio.processedPrefix
    );

    console.log(`Process completed successfully! Output saved as: ${config.audio.finalOutputFile}`);
  } catch (error) {
    console.error('Application error:', error.message);
    process.exit(1);
  } finally {
    // Cleanup: stop server if it was started
    if (config.defaultService === 'resemble') {
      try {
        await server.stopServer();
      } catch (error) {
        console.error('Error stopping server:', error.message);
      }
    }
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the application
main();
