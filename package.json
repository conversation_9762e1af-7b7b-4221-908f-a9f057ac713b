{"name": "pilgrim-audio-breaker", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "process": "node fragment-and-process.js", "fragment": "node fragment-and-process.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@deepgram/sdk": "^3.4.1", "@resemble/node": "^3.4.0", "axios": "^1.7.2", "dotenv": "^16.3.1", "ffmpeg-static": "^5.2.0", "ffprobe-static": "^3.1.0", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.0", "mp3-duration": "^1.1.0", "node-fetch": "^2.7.0", "node-wav": "^0.0.2", "wav": "^1.0.2", "wav-encoder": "^1.3.0", "whisper-node": "^1.1.1"}}