const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { fragmentAndProcess } = require('./fragment-and-process');
const audio = require('./lib/audio');
const config = require('./lib/config');

const app = express();
const PORT = process.env.PORT || 3000;

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, config.audio.uploadsDirectory);
  },
  filename: (req, file, cb) => {
    // Keep original filename with timestamp to avoid conflicts
    const timestamp = Date.now();
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext);
    cb(null, `${name}_${timestamp}${ext}`);
  }
});

const upload = multer({ 
  storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.mp3', '.wav', '.m4a', '.aac', '.flac', '.ogg', '.wma'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Supported formats: MP3, WAV, M4A, AAC, FLAC, OGG, WMA'));
    }
  },
  limits: {
    fileSize: 500 * 1024 * 1024 // 500MB limit
  }
});

// Middleware
app.use(express.json());
app.use(express.static('public'));

// Ensure all required directories exist
async function ensureDirectories() {
  try {
    await fs.mkdir(config.audio.uploadsDirectory, { recursive: true });
    await fs.mkdir(config.audio.fragmentsDirectory, { recursive: true });
    await fs.mkdir(config.audio.outputDirectory, { recursive: true });
    await fs.mkdir(config.audio.finalOutputDirectory, { recursive: true });
    console.log('📁 Created directory structure:');
    console.log(`   - Uploads: ${config.audio.uploadsDirectory}`);
    console.log(`   - Fragments: ${config.audio.fragmentsDirectory}`);
    console.log(`   - Processed: ${config.audio.outputDirectory}`);
    console.log(`   - Final Output: ${config.audio.finalOutputDirectory}`);
  } catch (error) {
    console.warn('Directory creation warning:', error.message);
  }
}

// API Routes

// Health check endpoint
app.get('/api/status', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Find optimal threshold endpoint
app.post('/api/process', upload.single('audioFile'), async (req, res) => {
  try {
    const { action } = req.body;
    
    // Set response headers for streaming
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Transfer-Encoding', 'chunked');

    const sendProgress = (type, data) => {
      res.write(JSON.stringify({ type, ...data }) + '\n');
    };

    const sendLog = (message) => {
      res.write(JSON.stringify({ type: 'log', message }) + '\n');
    };

    switch (action) {
      case 'findThreshold':
        await handleFindThreshold(req, res, sendProgress, sendLog);
        break;
      
      case 'fragmentOnly':
        await handleFragmentOnly(req, res, sendProgress, sendLog);
        break;
      
      case 'fullProcess':
        await handleFullProcess(req, res, sendProgress, sendLog);
        break;
      
      case 'continueProcess':
        await handleContinueProcess(req, res, sendProgress, sendLog);
        break;
      
      default:
        res.status(400).json({ error: 'Invalid action' });
        return;
    }

    res.end();

  } catch (error) {
    console.error('Processing error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Handle find threshold action
async function handleFindThreshold(req, res, sendProgress, sendLog) {
  const audioFile = req.file.path;
  
  sendLog('🔍 Starting threshold analysis...');
  sendProgress('progress', { percentage: 10, message: 'Analyzing audio file...' });

  const thresholds = [-20, -25, -30, -35, -40, -45, -50, -55, -60];
  const durations = [0.5, 0.3, 0.2, 0.1];
  const results = [];

  let progress = 10;
  const totalTests = thresholds.length * durations.length;
  let currentTest = 0;

  for (const threshold of thresholds) {
    for (const duration of durations) {
      currentTest++;
      progress = 10 + (currentTest / totalTests) * 80;
      
      sendProgress('progress', { 
        percentage: progress, 
        message: `Testing ${threshold}dB, ${duration}s...` 
      });

      try {
        const silencePoints = await findSilencePoints(audioFile, threshold, duration);
        const recommendation = categorizeThreshold(silencePoints.length);
        
        results.push({
          threshold,
          duration,
          silencePoints: silencePoints.length,
          recommendation
        });

        sendLog(`${threshold}dB, ${duration}s: ${silencePoints.length} points (${recommendation})`);

      } catch (error) {
        sendLog(`❌ Error testing ${threshold}dB: ${error.message}`);
      }
    }
  }

  sendProgress('progress', { percentage: 100, message: 'Analysis complete!' });
  sendProgress('result', { thresholdAnalysis: results });

  // Clean up uploaded file
  try {
    await fs.unlink(audioFile);
  } catch (error) {
    console.warn('Could not delete uploaded file:', error.message);
  }
}

// Handle fragment only action
async function handleFragmentOnly(req, res, sendProgress, sendLog) {
  const audioFile = req.file.path;
  const options = getProcessingOptions(req.body);

  sendLog('✂️ Starting fragmentation...');
  sendProgress('progress', { percentage: 20, message: 'Analyzing audio...' });

  try {
    // Get audio info
    const audioInfo = await audio.getAudioInfo(audioFile);
    sendLog(`📊 Duration: ${audioInfo.duration.toFixed(2)}s`);

    sendProgress('progress', { percentage: 40, message: 'Fragmenting audio...' });

    // Fragment the audio
    const fragments = await audio.fragmentAudioFile(audioFile, config.audio.fragmentPrefix, options);
    
    sendLog(`✅ Created ${fragments.length} fragments`);
    sendProgress('progress', { percentage: 100, message: 'Fragmentation complete!' });

    const fragmentResults = fragments.map(fragment => ({
      name: path.basename(fragment),
      path: fragment
    }));

    sendProgress('result', { fragments: fragmentResults });

  } catch (error) {
    sendLog(`❌ Fragmentation error: ${error.message}`);
    throw error;
  } finally {
    // Clean up uploaded file
    try {
      await fs.unlink(audioFile);
    } catch (error) {
      console.warn('Could not delete uploaded file:', error.message);
    }
  }
}

// Handle full process action
async function handleFullProcess(req, res, sendProgress, sendLog) {
  const audioFile = req.file.path;
  const options = getProcessingOptions(req.body);

  sendLog('🤖 Starting full processing...');
  
  try {
    // This would integrate with the existing fragmentAndProcess function
    // For now, we'll simulate the process
    sendProgress('progress', { percentage: 20, message: 'Fragmenting audio...' });
    await new Promise(resolve => setTimeout(resolve, 2000));

    sendProgress('progress', { percentage: 60, message: 'Processing with AI...' });
    await new Promise(resolve => setTimeout(resolve, 3000));

    sendProgress('progress', { percentage: 90, message: 'Combining results...' });
    await new Promise(resolve => setTimeout(resolve, 1000));

    sendProgress('progress', { percentage: 100, message: 'Processing complete!' });
    sendLog('✅ Full processing completed');

    // Return mock results
    sendProgress('result', {
      fragments: [
        { name: 'fragment_1.wav', path: './fragment_1.wav' },
        { name: 'fragment_2.wav', path: './fragment_2.wav' }
      ],
      processed: [
        { name: 'processed_1.mp3', path: './processed/processed_1.mp3' },
        { name: 'processed_2.mp3', path: './processed/processed_2.mp3' }
      ],
      final: { name: 'final_output.mp3', path: './final_output.mp3' }
    });

  } catch (error) {
    sendLog(`❌ Processing error: ${error.message}`);
    throw error;
  } finally {
    // Clean up uploaded file
    try {
      await fs.unlink(audioFile);
    } catch (error) {
      console.warn('Could not delete uploaded file:', error.message);
    }
  }
}

// Handle continue process action
async function handleContinueProcess(req, res, sendProgress, sendLog) {
  sendLog('▶️ Processing existing fragments...');
  
  try {
    // Find existing fragments
    const fragments = await audio.findWavFragments(config.audio.fragmentsDirectory, config.audio.fragmentPrefix);
    
    if (fragments.length === 0) {
      throw new Error('No existing fragments found');
    }

    sendLog(`📁 Found ${fragments.length} existing fragments`);
    sendProgress('progress', { percentage: 30, message: 'Processing fragments...' });

    // Simulate processing
    await new Promise(resolve => setTimeout(resolve, 3000));

    sendProgress('progress', { percentage: 100, message: 'Processing complete!' });
    sendLog('✅ Fragment processing completed');

    sendProgress('result', {
      processed: fragments.map(fragment => ({
        name: `processed_${path.basename(fragment)}`,
        path: `./processed/processed_${path.basename(fragment)}`
      })),
      final: { name: 'final_output.mp3', path: './final_output.mp3' }
    });

  } catch (error) {
    sendLog(`❌ Processing error: ${error.message}`);
    throw error;
  }
}

// Download endpoint
app.get('/download/:filename', async (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(__dirname, filename);
    
    // Check if file exists
    await fs.access(filePath);
    
    res.download(filePath, filename);
  } catch (error) {
    res.status(404).json({ error: 'File not found' });
  }
});

// Helper functions
function getProcessingOptions(body) {
  return {
    maxDuration: parseInt(body.maxDuration) || 240,
    maxAbsoluteDuration: 260,
    silenceThreshold: parseInt(body.silenceThreshold) || -40,
    minSilenceDuration: parseFloat(body.minSilenceDuration) || 0.3,
    cleanup: body.cleanup === 'true'
  };
}

async function findSilencePoints(inputFile, threshold, duration) {
  const { exec } = require('child_process');
  const util = require('util');
  const execPromise = util.promisify(exec);

  const silenceCommand = `ffmpeg -i "${inputFile}" -af silencedetect=noise=${threshold}dB:d=${duration} -f null - 2>&1`;
  const { stdout, stderr } = await execPromise(silenceCommand);
  const output = stdout + stderr;

  const silencePoints = [];
  const silenceRegex = /silence_start: ([\d.]+)/g;
  let match;

  while ((match = silenceRegex.exec(output)) !== null) {
    silencePoints.push(parseFloat(match[1]));
  }

  return silencePoints;
}

function categorizeThreshold(silencePointCount) {
  if (silencePointCount >= 2 && silencePointCount <= 50) {
    return 'GOOD';
  } else if (silencePointCount > 50) {
    return 'TOO_MANY';
  } else if (silencePointCount > 0) {
    return 'FEW';
  } else {
    return 'NONE';
  }
}

// Start server
async function startServer() {
  await ensureDirectories();
  
  app.listen(PORT, () => {
    console.log(`🎵 Pilgrim Audio Breaker Web Server running on http://localhost:${PORT}`);
    console.log(`📁 Upload directory: ${config.audio.uploadsDirectory}`);
    console.log(`📊 Processed directory: ${config.audio.outputDirectory}`);
    console.log(`🎯 Final output directory: ${config.audio.finalOutputDirectory}`);
  });
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  process.exit(0);
});

// Start the server
startServer().catch(console.error);

module.exports = app;
