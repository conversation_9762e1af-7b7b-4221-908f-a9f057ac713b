const fs = require('fs').promises;
const path = require('path');
const config = require('./lib/config');
const elevenlabs = require('./lib/services/elevenlabs');
const resemble = require('./lib/services/resemble');
const server = require('./lib/server');
const audio = require('./lib/audio');

/**
 * Main workflow: Fragment audio file and then process fragments
 * @param {string} inputFile - Path to the input audio file
 * @param {Object} options - Processing options
 */
async function fragmentAndProcess(inputFile, options = {}) {
  const {
    fragmentPrefix = config.audio.fragmentPrefix,
    maxDuration = config.audio.maxFragmentDuration,
    skipFragmentation = false,
    testMode = false,
    fragmentOnly = false,
    findOptimalThreshold = false
  } = options;

  console.log('=== Pilgrim Audio Breaker - Fragment & Process ===');
  console.log(`Input file: ${inputFile}`);

  // Handle special test modes
  if (findOptimalThreshold) {
    console.log('=== FINDING OPTIMAL SILENCE THRESHOLD ===');
    await findOptimalSilenceThreshold(inputFile);
    return;
  }

  try {
    // Validate environment (skip for fragment-only mode)
    if (!fragmentOnly) {
      console.log(`Using ${config.defaultService} service for audio processing`);
      await validateEnvironment();
    }

    let fragments;

    if (skipFragmentation) {
      // Just find existing fragments
      console.log('Skipping fragmentation, looking for existing fragments...');
      fragments = await audio.findWavFragments(
        config.audio.inputDirectory,
        config.audio.fragmentPrefix
      );

      if (fragments.length === 0) {
        throw new Error('No existing fragments found. Remove --skip-fragmentation to create them.');
      }
    } else {
      // Check if input file exists
      try {
        await fs.access(inputFile);
      } catch (error) {
        throw new Error(`Input file not found: ${inputFile}`);
      }

      // Get audio info
      console.log('Analyzing input file...');
      const audioInfo = await audio.getAudioInfo(inputFile);
      console.log(`Duration: ${audioInfo.duration.toFixed(2)}s, Sample Rate: ${audioInfo.sampleRate}Hz, Channels: ${audioInfo.channels}`);

      // Fragment the audio file
      console.log(`Fragmenting audio into ${maxDuration}s segments (max 260s)...`);
      fragments = await audio.fragmentAudioFile(inputFile, fragmentPrefix, {
        maxDuration,
        maxAbsoluteDuration: 260, // Hard limit
        silenceThreshold: config.audio.silenceThreshold,
        minSilenceDuration: config.audio.minSilenceDuration
      });
    }

    // If fragment-only mode, stop here
    if (fragmentOnly) {
      console.log('\n=== FRAGMENTATION COMPLETE ===');
      console.log('Fragment-only mode: Stopping before voice processing.');
      console.log('Generated fragments:');
      fragments.forEach((fragment, index) => {
        console.log(`  ${index + 1}. ${path.basename(fragment)}`);
      });

      // Ask user what to do next
      await askUserNextStep(fragments);
      return;
    }

    console.log(`Found ${fragments.length} fragments to process`);

    // Process all fragments
    await processAllFragments(fragments);
    console.log('All fragments processed successfully.');

    // Combine processed files
    console.log('Combining processed audio files...');
    await audio.combineAudioFiles(
      config.audio.outputDirectory,
      config.audio.finalOutputFile,
      config.audio.processedPrefix
    );

    console.log(`Process completed successfully! Output saved as: ${config.audio.finalOutputFile}`);
    
    // Optionally clean up fragments
    if (!skipFragmentation && process.argv.includes('--cleanup')) {
      console.log('Cleaning up fragment files...');
      for (const fragment of fragments) {
        try {
          await fs.unlink(fragment);
          console.log(`Removed: ${path.basename(fragment)}`);
        } catch (error) {
          console.warn(`Could not remove ${fragment}:`, error.message);
        }
      }
    }

  } catch (error) {
    console.error('Application error:', error.message);
    process.exit(1);
  } finally {
    // Cleanup: stop server if it was started
    if (config.defaultService === 'resemble') {
      try {
        await server.stopServer();
      } catch (error) {
        console.error('Error stopping server:', error.message);
      }
    }
  }
}

/**
 * Processes all audio fragments using the configured service
 * @param {string[]} fragmentPaths - Array of fragment file paths
 * @returns {Promise<void>}
 */
async function processAllFragments(fragmentPaths) {
  if (!fragmentPaths || fragmentPaths.length === 0) {
    throw new Error('No fragments provided for processing');
  }

  // Create output directory
  await fs.mkdir(config.audio.outputDirectory, { recursive: true });

  // Select service based on configuration
  const service = config.defaultService === 'resemble' ? resemble : elevenlabs;

  console.log(`Processing ${fragmentPaths.length} fragments using ${config.defaultService}...`);

  for (let i = 0; i < fragmentPaths.length; i++) {
    const inputPath = fragmentPaths[i];
    const outputPath = path.join(config.audio.outputDirectory, `${config.audio.processedPrefix}${i + 1}.mp3`);

    console.log(`Processing fragment ${i + 1}/${fragmentPaths.length}: ${path.basename(inputPath)}`);

    try {
      await service.processSpeechToSpeech(inputPath, outputPath);
    } catch (error) {
      console.error(`Failed to process fragment ${i + 1}:`, error.message);
      throw new Error(`Fragment processing failed at ${i + 1}/${fragmentPaths.length}: ${error.message}`);
    }
  }
}

/**
 * Finds the optimal silence threshold for an audio file
 * @param {string} inputFile - Path to the input audio file
 */
async function findOptimalSilenceThreshold(inputFile) {
  try {
    await fs.access(inputFile);
  } catch (error) {
    throw new Error(`Input file not found: ${inputFile}`);
  }

  console.log('Testing different silence thresholds...\n');

  const thresholds = [-20, -25, -30, -35, -40, -45, -50, -55, -60];
  const durations = [0.5, 0.3, 0.2, 0.1];

  for (const threshold of thresholds) {
    for (const duration of durations) {
      try {
        const silenceCommand = `ffmpeg -i "${inputFile}" -af silencedetect=noise=${threshold}dB:d=${duration} -f null - 2>&1`;
        const { stdout, stderr } = await require('util').promisify(require('child_process').exec)(silenceCommand);
        const output = stdout + stderr;

        const silencePoints = [];
        const silenceRegex = /silence_start: ([\d.]+)/g;
        let match;

        while ((match = silenceRegex.exec(output)) !== null) {
          silencePoints.push(parseFloat(match[1]));
        }

        console.log(`Threshold ${threshold}dB, Duration ${duration}s: ${silencePoints.length} silence points`);

        if (silencePoints.length >= 2 && silencePoints.length <= 50) {
          console.log(`  ✅ GOOD: This threshold would work well for fragmentation`);
        } else if (silencePoints.length > 50) {
          console.log(`  ⚠️  TOO MANY: Too sensitive, would create too many fragments`);
        } else if (silencePoints.length === 0) {
          console.log(`  ❌ NONE: No silence detected`);
        } else {
          console.log(`  ⚠️  FEW: Only ${silencePoints.length} silence point(s) found`);
        }

      } catch (error) {
        console.log(`Threshold ${threshold}dB, Duration ${duration}s: ERROR - ${error.message}`);
      }
    }
    console.log(''); // Empty line between thresholds
  }

  console.log('Recommendation: Use a threshold that shows "GOOD" status above.');
  console.log('You can set it in your .env file as SILENCE_THRESHOLD=-XX');
}

/**
 * Asks the user what to do next after fragmentation
 * @param {string[]} fragments - Array of fragment file paths
 */
async function askUserNextStep(fragments) {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('\nWhat would you like to do next?');
  console.log('1. Continue with voice processing (costs money!)');
  console.log('2. Just exit (keep fragments for later)');
  console.log('3. Delete fragments and exit');

  const answer = await new Promise((resolve) => {
    rl.question('\nEnter your choice (1-3): ', resolve);
  });

  rl.close();

  switch (answer.trim()) {
    case '1':
      console.log('\nContinuing with voice processing...');
      // Continue with processing
      await processAllFragments(fragments);
      console.log('All fragments processed successfully.');

      // Combine processed files
      console.log('Combining processed audio files...');
      await audio.combineAudioFiles(
        config.audio.outputDirectory,
        config.audio.finalOutputFile,
        config.audio.processedPrefix
      );
      console.log(`Process completed successfully! Output saved as: ${config.audio.finalOutputFile}`);
      break;

    case '2':
      console.log('\nExiting. Fragment files have been kept for later processing.');
      console.log('To process them later, run with --skip-fragmentation');
      break;

    case '3':
      console.log('\nDeleting fragment files...');
      for (const fragment of fragments) {
        try {
          await fs.unlink(fragment);
          console.log(`Removed: ${path.basename(fragment)}`);
        } catch (error) {
          console.warn(`Could not remove ${fragment}:`, error.message);
        }
      }
      console.log('Fragment files deleted.');
      break;

    default:
      console.log('Invalid choice. Exiting without changes.');
  }
}

/**
 * Validates the environment and configuration before processing
 * @returns {Promise<void>}
 */
async function validateEnvironment() {
  console.log('Validating environment...');

  // Validate configuration
  const configValidation = config.validateConfig();
  if (!configValidation.isValid) {
    throw new Error(`Configuration validation failed:\n${configValidation.errors.join('\n')}`);
  }

  // Validate audio tools
  const audioToolsValid = await audio.validateAudioTools();
  if (!audioToolsValid) {
    throw new Error('Required audio processing tools are not available');
  }

  // Validate service configuration
  const service = config.defaultService === 'resemble' ? resemble : elevenlabs;
  const serviceValid = await service.validateConfiguration();
  if (!serviceValid) {
    throw new Error(`${config.defaultService} service configuration is invalid`);
  }

  console.log('Environment validation completed successfully');
}

/**
 * Parse command line arguments
 */
function parseArguments() {
  const args = process.argv.slice(2);

  if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
    console.log(`
Usage: node fragment-and-process.js <input-file> [options]

Arguments:
  <input-file>          Path to the audio file to process (MP3, WAV, etc.)

Options:
  --skip-fragmentation  Skip fragmentation and process existing fragments
  --fragment-only      Only fragment the audio, don't process with AI (saves money!)
  --find-threshold     Test different silence thresholds to find optimal settings
  --cleanup            Remove fragment files after processing
  --max-duration <sec>  Target duration per fragment (default: 240s, max: 260s)
  --help, -h           Show this help message

Test Modes (no AI processing costs):
  --fragment-only      Fragment audio and ask what to do next
  --find-threshold     Analyze audio to find optimal silence detection settings

Examples:
  node fragment-and-process.js my-audio.mp3 --fragment-only
  node fragment-and-process.js my-audio.mp3 --find-threshold
  node fragment-and-process.js my-audio.mp3 --max-duration 180 --cleanup
  node fragment-and-process.js --skip-fragmentation
`);
    process.exit(0);
  }

  const inputFile = args[0];
  const skipFragmentation = args.includes('--skip-fragmentation');
  const fragmentOnly = args.includes('--fragment-only');
  const findOptimalThreshold = args.includes('--find-threshold');

  // Validate that we have an input file for modes that need it
  if (!skipFragmentation && !inputFile) {
    console.error('Error: Input file is required');
    process.exit(1);
  }

  let maxDuration = config.audio.maxFragmentDuration;
  const maxDurationIndex = args.indexOf('--max-duration');
  if (maxDurationIndex !== -1 && args[maxDurationIndex + 1]) {
    maxDuration = parseInt(args[maxDurationIndex + 1]);
    if (isNaN(maxDuration) || maxDuration <= 0) {
      console.error('Invalid max-duration value');
      process.exit(1);
    }
    if (maxDuration > 260) {
      console.error('Max duration cannot exceed 260 seconds (hard limit)');
      process.exit(1);
    }
  }

  return {
    inputFile: skipFragmentation ? null : inputFile,
    skipFragmentation,
    fragmentOnly,
    findOptimalThreshold,
    maxDuration
  };
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Main execution
if (require.main === module) {
  const options = parseArguments();
  fragmentAndProcess(options.inputFile, options);
}

module.exports = { fragmentAndProcess };
