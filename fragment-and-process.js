const fs = require('fs').promises;
const path = require('path');
const config = require('./lib/config');
const elevenlabs = require('./lib/services/elevenlabs');
const resemble = require('./lib/services/resemble');
const server = require('./lib/server');
const audio = require('./lib/audio');

/**
 * Main workflow: Fragment audio file and then process fragments
 * @param {string} inputFile - Path to the input audio file
 * @param {Object} options - Processing options
 */
async function fragmentAndProcess(inputFile, options = {}) {
  const {
    fragmentPrefix = config.audio.fragmentPrefix,
    maxDuration = config.audio.maxFragmentDuration,
    skipFragmentation = false
  } = options;

  console.log('=== Pilgrim Audio Breaker - Fragment & Process ===');
  console.log(`Input file: ${inputFile}`);
  console.log(`Using ${config.defaultService} service for audio processing`);

  try {
    // Validate environment
    await validateEnvironment();

    let fragments;

    if (skipFragmentation) {
      // Just find existing fragments
      console.log('Skipping fragmentation, looking for existing fragments...');
      fragments = await audio.findWavFragments(
        config.audio.inputDirectory,
        config.audio.fragmentPrefix
      );
      
      if (fragments.length === 0) {
        throw new Error('No existing fragments found. Remove --skip-fragmentation to create them.');
      }
    } else {
      // Check if input file exists
      try {
        await fs.access(inputFile);
      } catch (error) {
        throw new Error(`Input file not found: ${inputFile}`);
      }

      // Get audio info
      console.log('Analyzing input file...');
      const audioInfo = await audio.getAudioInfo(inputFile);
      console.log(`Duration: ${audioInfo.duration.toFixed(2)}s, Sample Rate: ${audioInfo.sampleRate}Hz, Channels: ${audioInfo.channels}`);

      // Fragment the audio file
      console.log(`Fragmenting audio into ${maxDuration}s segments...`);
      fragments = await audio.fragmentAudioFile(inputFile, fragmentPrefix, {
        maxDuration,
        silenceThreshold: config.audio.silenceThreshold,
        minSilenceDuration: config.audio.minSilenceDuration
      });
    }

    console.log(`Found ${fragments.length} fragments to process`);

    // Process all fragments
    await processAllFragments(fragments);
    console.log('All fragments processed successfully.');

    // Combine processed files
    console.log('Combining processed audio files...');
    await audio.combineAudioFiles(
      config.audio.outputDirectory,
      config.audio.finalOutputFile,
      config.audio.processedPrefix
    );

    console.log(`Process completed successfully! Output saved as: ${config.audio.finalOutputFile}`);
    
    // Optionally clean up fragments
    if (!skipFragmentation && process.argv.includes('--cleanup')) {
      console.log('Cleaning up fragment files...');
      for (const fragment of fragments) {
        try {
          await fs.unlink(fragment);
          console.log(`Removed: ${path.basename(fragment)}`);
        } catch (error) {
          console.warn(`Could not remove ${fragment}:`, error.message);
        }
      }
    }

  } catch (error) {
    console.error('Application error:', error.message);
    process.exit(1);
  } finally {
    // Cleanup: stop server if it was started
    if (config.defaultService === 'resemble') {
      try {
        await server.stopServer();
      } catch (error) {
        console.error('Error stopping server:', error.message);
      }
    }
  }
}

/**
 * Processes all audio fragments using the configured service
 * @param {string[]} fragmentPaths - Array of fragment file paths
 * @returns {Promise<void>}
 */
async function processAllFragments(fragmentPaths) {
  if (!fragmentPaths || fragmentPaths.length === 0) {
    throw new Error('No fragments provided for processing');
  }

  // Create output directory
  await fs.mkdir(config.audio.outputDirectory, { recursive: true });

  // Select service based on configuration
  const service = config.defaultService === 'resemble' ? resemble : elevenlabs;

  console.log(`Processing ${fragmentPaths.length} fragments using ${config.defaultService}...`);

  for (let i = 0; i < fragmentPaths.length; i++) {
    const inputPath = fragmentPaths[i];
    const outputPath = path.join(config.audio.outputDirectory, `${config.audio.processedPrefix}${i + 1}.mp3`);

    console.log(`Processing fragment ${i + 1}/${fragmentPaths.length}: ${path.basename(inputPath)}`);

    try {
      await service.processSpeechToSpeech(inputPath, outputPath);
    } catch (error) {
      console.error(`Failed to process fragment ${i + 1}:`, error.message);
      throw new Error(`Fragment processing failed at ${i + 1}/${fragmentPaths.length}: ${error.message}`);
    }
  }
}

/**
 * Validates the environment and configuration before processing
 * @returns {Promise<void>}
 */
async function validateEnvironment() {
  console.log('Validating environment...');

  // Validate configuration
  const configValidation = config.validateConfig();
  if (!configValidation.isValid) {
    throw new Error(`Configuration validation failed:\n${configValidation.errors.join('\n')}`);
  }

  // Validate audio tools
  const audioToolsValid = await audio.validateAudioTools();
  if (!audioToolsValid) {
    throw new Error('Required audio processing tools are not available');
  }

  // Validate service configuration
  const service = config.defaultService === 'resemble' ? resemble : elevenlabs;
  const serviceValid = await service.validateConfiguration();
  if (!serviceValid) {
    throw new Error(`${config.defaultService} service configuration is invalid`);
  }

  console.log('Environment validation completed successfully');
}

/**
 * Parse command line arguments
 */
function parseArguments() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
    console.log(`
Usage: node fragment-and-process.js <input-file> [options]

Arguments:
  <input-file>          Path to the audio file to process (MP3, WAV, etc.)

Options:
  --skip-fragmentation  Skip fragmentation and process existing fragments
  --cleanup            Remove fragment files after processing
  --max-duration <sec>  Maximum duration per fragment (default: 240s)
  --help, -h           Show this help message

Examples:
  node fragment-and-process.js my-audio.mp3
  node fragment-and-process.js podcast.wav --max-duration 180 --cleanup
  node fragment-and-process.js --skip-fragmentation
`);
    process.exit(0);
  }

  const inputFile = args[0];
  const skipFragmentation = args.includes('--skip-fragmentation');
  
  let maxDuration = config.audio.maxFragmentDuration;
  const maxDurationIndex = args.indexOf('--max-duration');
  if (maxDurationIndex !== -1 && args[maxDurationIndex + 1]) {
    maxDuration = parseInt(args[maxDurationIndex + 1]);
    if (isNaN(maxDuration) || maxDuration <= 0) {
      console.error('Invalid max-duration value');
      process.exit(1);
    }
  }

  return {
    inputFile: skipFragmentation ? null : inputFile,
    skipFragmentation,
    maxDuration
  };
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Main execution
if (require.main === module) {
  const options = parseArguments();
  fragmentAndProcess(options.inputFile, options);
}

module.exports = { fragmentAndProcess };
