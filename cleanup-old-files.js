#!/usr/bin/env node

/**
 * Cleanup script to move old files to the new organized directory structure
 */

const fs = require('fs').promises;
const path = require('path');

async function cleanupOldFiles() {
  console.log('🧹 Cleaning up old files and organizing directory structure...\n');

  try {
    // Create new directory structure
    const directories = [
      './data',
      './data/input',
      './data/fragments', 
      './data/processed',
      './data/uploads',
      './data/output'
    ];

    for (const dir of directories) {
      await fs.mkdir(dir, { recursive: true });
      console.log(`📁 Created directory: ${dir}`);
    }

    // Find and move old fragment files
    const files = await fs.readdir('.');
    
    const fragmentFiles = files.filter(file => 
      file.startsWith('output_fragment_') && file.endsWith('.wav')
    );
    
    const processedFiles = files.filter(file => 
      file.startsWith('processed_') && file.endsWith('.mp3')
    );

    const finalOutputFiles = files.filter(file => 
      file === 'final_output.mp3'
    );

    const audioInputFiles = files.filter(file => {
      const ext = path.extname(file).toLowerCase();
      return ['.mp3', '.wav', '.m4a', '.aac', '.flac', '.ogg', '.wma'].includes(ext) &&
             !file.startsWith('output_fragment_') &&
             !file.startsWith('processed_') &&
             file !== 'final_output.mp3';
    });

    // Move fragment files
    if (fragmentFiles.length > 0) {
      console.log(`\n📦 Moving ${fragmentFiles.length} fragment files...`);
      for (const file of fragmentFiles) {
        const newName = file.replace('output_fragment_', 'fragment_');
        const newPath = path.join('./data/fragments', newName);
        await fs.rename(file, newPath);
        console.log(`   ${file} → ${newPath}`);
      }
    }

    // Move processed files
    if (processedFiles.length > 0) {
      console.log(`\n🤖 Moving ${processedFiles.length} processed files...`);
      for (const file of processedFiles) {
        const newPath = path.join('./data/processed', file);
        await fs.rename(file, newPath);
        console.log(`   ${file} → ${newPath}`);
      }
    }

    // Move final output files
    if (finalOutputFiles.length > 0) {
      console.log(`\n🎯 Moving ${finalOutputFiles.length} final output files...`);
      for (const file of finalOutputFiles) {
        const newPath = path.join('./data/output', file);
        await fs.rename(file, newPath);
        console.log(`   ${file} → ${newPath}`);
      }
    }

    // Move input audio files
    if (audioInputFiles.length > 0) {
      console.log(`\n🎵 Moving ${audioInputFiles.length} input audio files...`);
      for (const file of audioInputFiles) {
        const newPath = path.join('./data/input', file);
        await fs.rename(file, newPath);
        console.log(`   ${file} → ${newPath}`);
      }
    }

    // Clean up old processed directory if it exists
    try {
      const oldProcessedFiles = await fs.readdir('./processed');
      if (oldProcessedFiles.length > 0) {
        console.log(`\n📂 Moving files from old ./processed directory...`);
        for (const file of oldProcessedFiles) {
          const oldPath = path.join('./processed', file);
          const newPath = path.join('./data/processed', file);
          await fs.rename(oldPath, newPath);
          console.log(`   ./processed/${file} → ${newPath}`);
        }
      }
      await fs.rmdir('./processed');
      console.log(`   🗑️  Removed old ./processed directory`);
    } catch (error) {
      // Directory doesn't exist or is not empty, that's fine
    }

    console.log('\n✅ Cleanup completed! New directory structure:');
    console.log('📁 ./data/');
    console.log('   ├── input/      # Original audio files');
    console.log('   ├── fragments/  # Audio fragments (.wav)');
    console.log('   ├── processed/  # AI-processed fragments (.mp3)');
    console.log('   ├── uploads/    # Web interface uploads (temporary)');
    console.log('   └── output/     # Final combined output files');

    console.log('\n💡 The root directory is now clean and organized!');

  } catch (error) {
    console.error('❌ Error during cleanup:', error.message);
    process.exit(1);
  }
}

// Run cleanup if this script is executed directly
if (require.main === module) {
  cleanupOldFiles();
}

module.exports = { cleanupOldFiles };
