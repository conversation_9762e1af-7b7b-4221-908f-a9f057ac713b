export interface GetProjectUsageSummarySchema extends Record<string, unknown> {
    start?: string;
    end: string;
    accessor?: string;
    tag?: string;
    method?: string;
    model?: string;
    multichannel?: boolean;
    interim_results?: boolean;
    punctuate?: boolean;
    ner?: boolean;
    utterances?: boolean;
    replace?: boolean;
    profanity_filter?: boolean;
    keywords?: boolean;
    detect_topics?: boolean;
    diarize?: boolean;
    search?: boolean;
    redact?: boolean;
    alternatives?: boolean;
    numerals?: boolean;
    smart_format?: boolean;
}
//# sourceMappingURL=GetProjectUsageSummarySchema.d.ts.map