export interface GetProjectKeysResponse {
    api_keys: GetProjectKeyResponse[];
}
export interface GetProjectKeyResponse {
    member: Member;
    api_key: Key;
}
interface Member {
    member_id: string;
    email: string;
    first_name: string;
    last_name: string;
}
interface Key {
    api_key_id: string;
    comment?: string;
    scopes: string[];
    tags?: string[];
    created: string;
}
export {};
//# sourceMappingURL=GetProjectKeysResponse.d.ts.map