{"version": 3, "file": "ReadRestClient.js", "sourceRoot": "", "sources": ["../../../src/packages/ReadRestClient.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,4CAA4F;AAC5F,0CAA+D;AAS/D,6DAA0D;AAE1D;;;;;;;;;;GAUG;AACH,MAAa,cAAe,SAAQ,uCAAkB;IAAtD;;QACS,cAAS,GAAW,MAAM,CAAC;IA6KpC,CAAC;IA3KC;;;;;;;OAOG;IACG,UAAU,CACd,MAAiB,EACjB,OAAuB,EACvB,QAAQ,GAAG,eAAe;;YAE1B,IAAI;gBACF,IAAI,IAAI,CAAC;gBAET,IAAI,IAAA,qBAAW,EAAC,MAAM,CAAC,EAAE;oBACvB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;iBAC/B;qBAAM;oBACL,MAAM,IAAI,sBAAa,CAAC,qBAAqB,CAAC,CAAC;iBAChD;gBAED,IAAI,OAAO,KAAK,SAAS,IAAI,UAAU,IAAI,OAAO,EAAE;oBAClD,MAAM,IAAI,sBAAa,CACrB,qIAAqI,CACtI,CAAC;iBACH;gBAED,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,gBAAO,EAAE,EAAK,OAAO,EAAG,CAAC;gBAC3E,MAAM,MAAM,GAAwB,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CACpF,MAAM,CAAC,IAAI,EAAE,CACd,CAAC;gBAEF,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;aAChC;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,IAAA,wBAAe,EAAC,KAAK,CAAC,EAAE;oBAC1B,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;iBAChC;gBAED,MAAM,KAAK,CAAC;aACb;QACH,CAAC;KAAA;IAED;;;;;;;OAOG;IACG,WAAW,CACf,MAAkB,EAClB,OAAuB,EACvB,QAAQ,GAAG,eAAe;;YAE1B,IAAI;gBACF,IAAI,IAAI,CAAC;gBAET,IAAI,IAAA,sBAAY,EAAC,MAAM,CAAC,EAAE;oBACxB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;iBAC/B;qBAAM;oBACL,MAAM,IAAI,sBAAa,CAAC,qBAAqB,CAAC,CAAC;iBAChD;gBAED,IAAI,OAAO,KAAK,SAAS,IAAI,UAAU,IAAI,OAAO,EAAE;oBAClD,MAAM,IAAI,sBAAa,CACrB,gIAAgI,CACjI,CAAC;iBACH;gBAED,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,gBAAO,EAAE,EAAK,OAAO,EAAG,CAAC;gBAC3E,MAAM,MAAM,GAAwB,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CACpF,MAAM,CAAC,IAAI,EAAE,CACd,CAAC;gBAEF,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;aAChC;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,IAAA,wBAAe,EAAC,KAAK,CAAC,EAAE;oBAC1B,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;iBAChC;gBAED,MAAM,KAAK,CAAC;aACb;QACH,CAAC;KAAA;IAED;;;;;;;;OAQG;IACG,kBAAkB,CACtB,MAAiB,EACjB,QAAqB,EACrB,OAAuB,EACvB,QAAQ,GAAG,eAAe;;YAE1B,IAAI;gBACF,IAAI,IAAI,CAAC;gBAET,IAAI,IAAA,qBAAW,EAAC,MAAM,CAAC,EAAE;oBACvB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;iBAC/B;qBAAM;oBACL,MAAM,IAAI,sBAAa,CAAC,qBAAqB,CAAC,CAAC;iBAChD;gBAED,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CACnC,QAAQ,EACR,EAAE,kCACG,OAAO,KAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,IAC5C,CAAC;gBACF,MAAM,MAAM,GAAyB,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CACrF,MAAM,CAAC,IAAI,EAAE,CACd,CAAC;gBAEF,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;aAChC;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,IAAA,wBAAe,EAAC,KAAK,CAAC,EAAE;oBAC1B,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;iBAChC;gBAED,MAAM,KAAK,CAAC;aACb;QACH,CAAC;KAAA;IAED;;;;;;;;OAQG;IACG,mBAAmB,CACvB,MAAkB,EAClB,QAAqB,EACrB,OAAuB,EACvB,QAAQ,GAAG,eAAe;;YAE1B,IAAI;gBACF,IAAI,IAAI,CAAC;gBAET,IAAI,IAAA,sBAAY,EAAC,MAAM,CAAC,EAAE;oBACxB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;iBAC/B;qBAAM;oBACL,MAAM,IAAI,sBAAa,CAAC,qBAAqB,CAAC,CAAC;iBAChD;gBAED,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CACnC,QAAQ,EACR,EAAE,kCACG,OAAO,KAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,IAC5C,CAAC;gBACF,MAAM,MAAM,GAAyB,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE;oBACrE,OAAO,EAAE,EAAE,cAAc,EAAE,sBAAsB,EAAE;iBACpD,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;gBAEnC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;aAChC;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,IAAA,wBAAe,EAAC,KAAK,CAAC,EAAE;oBAC1B,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;iBAChC;gBAED,MAAM,KAAK,CAAC;aACb;QACH,CAAC;KAAA;CACF;AA9KD,wCA8KC;AAE0B,oCAAU"}