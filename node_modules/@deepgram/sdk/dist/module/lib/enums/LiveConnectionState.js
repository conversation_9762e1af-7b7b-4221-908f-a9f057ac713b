/**
 * Enum representing the different states of a live connection.
 *
 * @deprecated Since 3.4. Use `SOCKET_STATES` for generic socket connection states instead.
 */
export var LiveConnectionState;
(function (LiveConnectionState) {
    LiveConnectionState[LiveConnectionState["CONNECTING"] = 0] = "CONNECTING";
    LiveConnectionState[LiveConnectionState["OPEN"] = 1] = "OPEN";
    LiveConnectionState[LiveConnectionState["CLOSING"] = 2] = "CLOSING";
    LiveConnectionState[LiveConnectionState["CLOSED"] = 3] = "CLOSED";
})(LiveConnectionState || (LiveConnectionState = {}));
//# sourceMappingURL=LiveConnectionState.js.map