export interface SpeakSchema extends Record<string, unknown> {
    /**
     * The model, voice, language, and version of the voice.
     * Follows the format of[modelname]-[voicename]-[language]-[version].
     */
    model?: string;
    /**
     * Encoding options for the output audio. Default is 'mp3'.
     */
    encoding?: "linear16" | "mulaw" | "alaw" | "mp3" | "opus" | "flac" | "aac";
    /**
     * File format wrapper for the audio.
     */
    container?: string;
    /**
     * Sample rate of the audio output.
     */
    sample_rate?: number;
    /**
     * Bit rate of the audio output.
     */
    bit_rate?: number;
    [key: string]: unknown;
}
//# sourceMappingURL=SpeakSchema.d.ts.map