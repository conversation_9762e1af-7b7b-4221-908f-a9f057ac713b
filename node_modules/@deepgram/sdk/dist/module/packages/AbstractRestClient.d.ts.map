{"version": 3, "file": "AbstractRestClient.d.ts", "sourceRoot": "", "sources": ["../../../src/packages/AbstractRestClient.ts"], "names": [], "mappings": ";;AACA,OAAO,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC;AAElC,OAAO,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,iBAAiB,EAAE,MAAM,oBAAoB,CAAC;AACjF,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,qBAAqB,EAAE,MAAM,cAAc,CAAC;AAIrD;;;GAGG;AACH,8BAAsB,kBAAmB,SAAQ,cAAc;IAC7D,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC;IAEvB;;;;;OAKG;gBACS,OAAO,EAAE,qBAAqB;IAkB1C;;;;;OAKG;IACH,SAAS,CAAC,gBAAgB,CAAC,GAAG,EAAE,GAAG,GAAG,MAAM;IAI5C;;;;;;OAMG;cACa,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,EAAE,GAAG,KAAK,IAAI;IAiB3E;;;;;;;OAOG;IACH,SAAS,CAAC,kBAAkB,CAC1B,MAAM,EAAE,iBAAiB,EACzB,aAAa,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,QAAQ,GAAG,YAAY,EACzD,OAAO,CAAC,EAAE,YAAY,GACrB,YAAY;IAiBf;;;;;;;;OAQG;cACa,cAAc,CAC5B,MAAM,EAAE,KAAK,GAAG,QAAQ,EACxB,GAAG,EAAE,GAAG,EACR,OAAO,CAAC,EAAE,YAAY,GACrB,OAAO,CAAC,QAAQ,CAAC;cACJ,cAAc,CAC5B,MAAM,EAAE,MAAM,GAAG,KAAK,GAAG,OAAO,EAChC,GAAG,EAAE,GAAG,EACR,IAAI,EAAE,MAAM,GAAG,MAAM,GAAG,QAAQ,EAChC,OAAO,CAAC,EAAE,YAAY,GACrB,OAAO,CAAC,QAAQ,CAAC;IAmBpB;;;;;;OAMG;cACa,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC;IAInE;;;;;;;OAOG;cACa,IAAI,CAClB,GAAG,EAAE,GAAG,EACR,IAAI,EAAE,MAAM,GAAG,MAAM,GAAG,QAAQ,EAChC,OAAO,CAAC,EAAE,YAAY,GACrB,OAAO,CAAC,GAAG,CAAC;IAIf;;;;;;;OAOG;cACa,GAAG,CACjB,GAAG,EAAE,GAAG,EACR,IAAI,EAAE,MAAM,GAAG,MAAM,GAAG,QAAQ,EAChC,OAAO,CAAC,EAAE,YAAY,GACrB,OAAO,CAAC,GAAG,CAAC;IAIf;;;;;;;OAOG;cACa,KAAK,CACnB,GAAG,EAAE,GAAG,EACR,IAAI,EAAE,MAAM,GAAG,MAAM,GAAG,QAAQ,EAChC,OAAO,CAAC,EAAE,YAAY,GACrB,OAAO,CAAC,GAAG,CAAC;IAIf;;;;;;OAMG;cACa,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC;IAItE;;;OAGG;IACH,IAAI,KAAK,IAAI,OAAO,CAEnB;CACF;AAED,OAAO,EAAE,kBAAkB,IAAI,qBAAqB,EAAE,CAAC"}