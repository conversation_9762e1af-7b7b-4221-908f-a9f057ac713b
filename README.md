# 🎵 Pilgrim Audio Breaker

An intelligent audio fragmentation and voice processing tool that splits audio files at natural speech pauses and processes them through AI voice conversion services.

## ✨ Features

- **🧠 Smart Fragmentation**: Breaks audio at natural speech pauses, not mid-word
- **⚡ Multiple AI Services**: Supports ElevenLabs and Resemble.ai
- **🎯 Flexible Duration**: Target 240s fragments with 260s hard limit
- **💰 Cost Control**: Test modes that don't use AI APIs
- **🔧 Interactive CLI**: File selection and user prompts
- **📊 Silence Analysis**: Find optimal silence detection thresholds
- **🚀 NPM Scripts**: Easy-to-use commands for all workflows
- **🎛️ Comprehensive Options**: Flexible configuration and processing modes

## Project Structure

```
├── lib/                          # Core application modules
│   ├── audio.js                  # Audio processing utilities
│   ├── config.js                 # Configuration management
│   ├── server.js                 # HTTP server for file serving
│   └── services/                 # AI service integrations
│       ├── elevenlabs.js         # ElevenLabs API integration
│       └── resemble.js           # Resemble.ai API integration
├── fragment-and-process.js       # Complete workflow script
├── index.js                      # Process existing fragments
├── package.json                  # Dependencies and scripts
├── .env.example                  # Environment configuration template
├── .gitignore                    # Git ignore patterns
└── README.md                     # This documentation
```

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Copy the environment template:
   ```bash
   cp .env.example .env
   ```
4. Edit `.env` with your API keys and configuration

## Configuration

The application uses environment variables for configuration. See `.env.example` for all available options.

### Required Configuration

#### For ElevenLabs:
- `ELEVENLABS_API_KEY`: Your ElevenLabs API key
- `ELEVENLABS_VOICE_ID`: Voice ID to use for processing

#### For Resemble.ai:
- `RESEMBLE_API_KEY`: Your Resemble.ai API key
- `RESEMBLE_PROJECT_UUID`: Project UUID
- `RESEMBLE_VOICE_UUID`: Voice UUID

### Service Selection

Set `DEFAULT_SERVICE` to either `"elevenlabs"` or `"resemble"` to choose your preferred service.

## 🚀 Quick Start

### 🌐 Web Interface (Recommended)

```bash
# Start the web server
npm run web

# Then open http://localhost:3000 in your browser
```

**Features:**
- 📁 **File picker** for easy audio file selection
- 🎛️ **Visual controls** for all processing options
- 📊 **Real-time progress** updates and logs
- 🔍 **Interactive threshold analysis** with recommendations
- 💾 **Download links** for all generated files

### 💻 Command Line Interface

```bash
# Interactive mode - select file from current directory
npm start

# Fragment only (no AI processing costs)
npm run fragment

# Find optimal silence threshold (no costs)
npm run threshold

# Process existing fragments (costs money!)
npm run continue

# Show help
npm run help
```

### 📋 Available Scripts

| Script | Description | Costs Money? |
|--------|-------------|--------------|
| `npm run web` | **Start web interface** | ❌ No (server only) |
| `npm run dev` | Start web server with auto-reload | ❌ No (development) |
| `npm start` | Interactive CLI mode | Only if you choose to continue |
| `npm run process` | Full processing pipeline (CLI) | ✅ Yes |
| `npm run fragment` | Fragment only (CLI) | ❌ No |
| `npm run threshold` | Find optimal silence settings (CLI) | ❌ No |
| `npm run continue` | Process existing fragments (CLI) | ✅ Yes |
| `npm run help` | Show detailed help | ❌ No |

## 💡 Usage Examples

### Test Mode (Free - No API Costs)
```bash
# Find best silence threshold for your audio
npm run threshold

# Fragment audio and decide later what to do
npm run fragment

# Interactive file selection for fragmentation
npm run fragment
```

### Production Mode (Costs Money)
```bash
# Full processing with interactive file selection
npm start

# Process specific file
npm run process podcast.mp3

# Process with custom settings
npm run process lecture.wav --max-duration 120 --cleanup
```

### Advanced Usage
```bash
# Fragment specific file with custom duration
npm run fragment my-audio.mp3 --max-duration 180

# Process existing fragments (if you already have fragments)
npm run continue

# Direct node.js usage (alternative)
node fragment-and-process.js your-audio-file.mp3 --fragment-only
```

## 🎛️ Options & Configuration

### Command Line Options

- `--fragment-only`: Stop after fragmentation (saves money)
- `--find-threshold`: Analyze silence detection settings
- `--skip-fragmentation`: Process existing fragments
- `--max-duration <seconds>`: Target fragment duration (max 260s)
- `--cleanup`: Remove fragment files after processing

### 📁 Supported Audio Formats

- **MP3** - Most common format
- **WAV** - Uncompressed audio
- **M4A** - Apple audio format
- **AAC** - Advanced Audio Coding
- **FLAC** - Lossless compression
- **OGG** - Open source format
- **WMA** - Windows Media Audio

### 🔧 How Smart Fragmentation Works

1. **Analysis**: Detects silence points in your audio using FFmpeg
2. **Scoring**: Evaluates different silence thresholds to find optimal settings
3. **Smart Breaking**: Finds natural speech breaks near target duration (240s)
4. **Hard Limits**: Never exceeds 260s per fragment to respect API limits
5. **Tolerance**: Allows ±30s variance to find natural pauses

## Output

The application creates the following structure:

```
your-project/
├── your-audio.mp3                   # Your original file
├── output_fragment_0.wav            # Generated fragments (auto-created)
├── output_fragment_1.wav
├── output_fragment_2.wav
├── processed/                       # AI-processed fragments (auto-created)
│   ├── processed_1.mp3
│   ├── processed_2.mp3
│   └── processed_3.mp3
└── final_output.mp3                 # 🎯 Your final result!
```

**Note**: Generated files and directories are automatically created during processing and are excluded from version control via `.gitignore`.

## Requirements

- Node.js 14+ 
- FFmpeg (for audio processing)
- Valid API keys for your chosen service

## Error Handling

The application includes comprehensive error handling:
- Configuration validation before processing
- Service connectivity checks
- Individual fragment processing error recovery
- Graceful cleanup on exit

## Development

The codebase follows modern JavaScript practices:
- Modular architecture with clear separation of concerns
- Comprehensive JSDoc documentation
- Consistent error handling patterns
- Environment-based configuration
- Input validation and sanitization

## 🌐 Web Interface Features

The web interface provides a modern, user-friendly way to use all Pilgrim Audio Breaker features:

### 🎯 **Core Features**
- **📁 Drag & Drop File Upload**: Easy file selection with validation
- **🎛️ Visual Controls**: Intuitive sliders and inputs for all options
- **📊 Real-time Progress**: Live updates during processing
- **🔍 Interactive Analysis**: Threshold finder with visual recommendations
- **💾 Download Manager**: Direct download links for all generated files
- **🔒 Privacy First**: Files processed locally and auto-deleted

### 🚀 **Getting Started with Web UI**

1. **Start the server:**
   ```bash
   npm run web
   ```

2. **Open your browser:**
   ```
   http://localhost:3000
   ```

3. **Upload and process:**
   - Select your audio file
   - Adjust settings as needed
   - Choose your processing mode
   - Download results

### 💡 **Web Interface Advantages**

- **Beginner Friendly**: No command line knowledge required
- **Visual Feedback**: See progress and logs in real-time
- **Error Handling**: Clear error messages and validation
- **Mobile Responsive**: Works on tablets and phones
- **No Installation**: Just run the server and use any browser

## 📝 License

ISC
