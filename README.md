# Pilgrim Audio Breaker

A Node.js application for processing audio fragments using AI-powered speech-to-speech conversion services.

## Features

- **Audio Fragmentation**: Automatically split long audio files into manageable fragments
- **Multiple Service Support**: Choose between ElevenLabs and Resemble.ai for audio processing
- **Batch Processing**: Process multiple audio fragments automatically
- **Audio Combination**: Automatically combine processed fragments into a single output file
- **Flexible Configuration**: Environment-based configuration with sensible defaults
- **Error Handling**: Comprehensive error handling and validation
- **Modular Architecture**: Clean, organized codebase with separation of concerns
- **Complete Workflow**: One command to fragment, process, and combine audio

## Project Structure

```
├── lib/                          # Core application modules
│   ├── audio.js                  # Audio processing utilities
│   ├── config.js                 # Configuration management
│   ├── server.js                 # HTTP server for file serving
│   └── services/                 # AI service integrations
│       ├── elevenlabs.js         # ElevenLabs API integration
│       └── resemble.js           # Resemble.ai API integration
├── fragment-and-process.js       # Complete workflow script
├── index.js                      # Process existing fragments
├── package.json                  # Dependencies and scripts
├── .env.example                  # Environment configuration template
├── .gitignore                    # Git ignore patterns
└── README.md                     # This documentation
```

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Copy the environment template:
   ```bash
   cp .env.example .env
   ```
4. Edit `.env` with your API keys and configuration

## Configuration

The application uses environment variables for configuration. See `.env.example` for all available options.

### Required Configuration

#### For ElevenLabs:
- `ELEVENLABS_API_KEY`: Your ElevenLabs API key
- `ELEVENLABS_VOICE_ID`: Voice ID to use for processing

#### For Resemble.ai:
- `RESEMBLE_API_KEY`: Your Resemble.ai API key
- `RESEMBLE_PROJECT_UUID`: Project UUID
- `RESEMBLE_VOICE_UUID`: Voice UUID

### Service Selection

Set `DEFAULT_SERVICE` to either `"elevenlabs"` or `"resemble"` to choose your preferred service.

## Usage

### Option 1: Complete Workflow (Recommended)

**Start with a single audio file and let the app handle everything:**

```bash
node fragment-and-process.js your-audio-file.mp3
```

This will:
1. ✅ Fragment your audio file into manageable chunks
2. 🤖 Process each fragment through AI voice conversion
3. 🔗 Combine all processed fragments into a final output

**Advanced options:**
```bash
# Custom fragment duration (3 minutes instead of default 4)
node fragment-and-process.js podcast.wav --max-duration 180

# Clean up fragment files after processing
node fragment-and-process.js audio.mp3 --cleanup

# Show help
node fragment-and-process.js --help
```

### Option 2: Process Existing Fragments

If you already have fragmented files:

```bash
# Skip fragmentation and process existing fragments
node fragment-and-process.js --skip-fragmentation

# Or use the original method
node index.js
```

**For this option:**
1. Place your audio fragment files in the input directory (default: current directory)
2. Ensure fragments follow the naming pattern: `output_fragment_*.wav`
3. Run the application

### Option 3: Fragment Only

If you just want to split an audio file without processing:

```bash
node -e "
const audio = require('./lib/audio');
audio.fragmentAudioFile('your-file.mp3', 'output_fragment')
  .then(fragments => console.log('Created fragments:', fragments))
  .catch(console.error);
"
```

## Output

The application creates the following structure:

```
your-project/
├── your-audio.mp3                   # Your original file
├── output_fragment_0.wav            # Generated fragments (auto-created)
├── output_fragment_1.wav
├── output_fragment_2.wav
├── processed/                       # AI-processed fragments (auto-created)
│   ├── processed_1.mp3
│   ├── processed_2.mp3
│   └── processed_3.mp3
└── final_output.mp3                 # 🎯 Your final result!
```

**Note**: Generated files and directories are automatically created during processing and are excluded from version control via `.gitignore`.

## Requirements

- Node.js 14+ 
- FFmpeg (for audio processing)
- Valid API keys for your chosen service

## Error Handling

The application includes comprehensive error handling:
- Configuration validation before processing
- Service connectivity checks
- Individual fragment processing error recovery
- Graceful cleanup on exit

## Development

The codebase follows modern JavaScript practices:
- Modular architecture with clear separation of concerns
- Comprehensive JSDoc documentation
- Consistent error handling patterns
- Environment-based configuration
- Input validation and sanitization

## License

ISC
